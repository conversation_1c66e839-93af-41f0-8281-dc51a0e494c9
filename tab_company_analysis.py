# tab_company_analysis.py
# Updated to include dynamic capital call fees

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
from datetime import datetime
from plotly.subplots import make_subplots
from pandas.tseries.offsets import DateOffset

from financial_calculations import calculate_company_management_fees_with_hurdle, xirr, aggregate_cashflows_by_date
from data_loader import get_company_quarterly_fee, COMPANY_QUARTERLY_FEES

def display_company_analysis_tab(data_df, company_list, fee_balance, fee_date, q_fee, target_irr_param):
    # Find a valid default company (with book value)
    default_company = None
    for comp_iter in company_list:
        comp_data_iter = data_df[data_df["Deal Name"] == comp_iter]
        book_row_iter = comp_data_iter[comp_data_iter["Book Value"] > 0].sort_values("Date", ascending=False)
        if not book_row_iter.empty:
            default_company = comp_iter
            break
    
    if default_company is None:
        if company_list:
            default_company = company_list[0]
        else:
            st.sidebar.warning("No companies available to select.")
            return
    
    # Ensure default_company is in company_list before finding index
    default_company_index = 0
    if default_company and default_company in company_list:
        default_company_index = company_list.index(default_company)
    elif company_list:
        default_company = company_list[0]
        default_company_index = 0
    
    company = st.sidebar.selectbox("Select Portfolio Company", company_list, index=default_company_index, key="tab1_company_selectbox")
    exit_moic = st.sidebar.slider("Projected Exit MOIC", 0.5, 10.0, 3.0, 0.1, key="tab1_exit_moic_slider")
    exit_date_str = st.sidebar.text_input("Projected Exit Date", "2026-05-19", key="tab1_exit_date_input", help="Enter date in YYYY-MM-DD format")
    
    # Parse the date string
    try:
        exit_date = pd.to_datetime(datetime.datetime.strptime(exit_date_str, "%Y-%m-%d").date())
    except ValueError:
        exit_date = pd.to_datetime(datetime(2026, 5, 19))
        if exit_date_str != "2026-05-19":
            st.sidebar.warning("Invalid date format. Using default 2026-05-19.")
    
    # Filter data
    cdata = data_df[data_df["Deal Name"] == company]
    flows = [(row["Date"], row["Distributions"] + row["Contributions"]) for _, row in cdata.iterrows()]
    book_row = cdata[cdata["Book Value"] > 0].sort_values("Date", ascending=False)
    
    if book_row.empty:
        st.warning(f"No book value found for {company}. Please check the data.")
        return
        
    book_val = book_row.iloc[0]["Book Value"]
    book_date = book_row.iloc[0]["Date"] if not book_row.empty else None

    # Calculate exit value and add to cash flows
    exit_val = book_val * exit_moic
    baseline_cf_orig = flows + [(book_date, book_val)]
    modeled_cf_orig = flows + [(exit_date, exit_val)]

    # Calculate management fees using individual company method with dynamic capital calls
    fee_details = calculate_company_management_fees_with_hurdle(
        fee_date, 
        exit_date, 
        fee_balance,
        company,
        hurdle_rate=0.08,
        is_first_exit=True  # Company-level analysis assumes first exit
    )
    
    total_fees = fee_details['total_fees']
    priority_return = fee_details['priority_return']
    new_fees = fee_details['new_fees']
    company_quarterly_fee = fee_details['company_quarterly_fee']
    company_proportion = fee_details['company_proportion']
    company_initial_balance = fee_details['initial_balance']
    
    # Extract capital call fee details (dynamic calculation)
    capital_call_fees = fee_details.get('capital_call_fees', 0)
    capital_call_pr = fee_details.get('capital_call_pr', 0)
    mgmt_fees_only = fee_details.get('mgmt_fees_only', total_fees)
    
    # Calculate IRRs
    baseline_cf_agg = aggregate_cashflows_by_date(baseline_cf_orig) 
    baseline_irr = xirr(baseline_cf_agg)
    
    calculated_new_baseline_net_irr = None
    if baseline_irr is not None:
        calculated_new_baseline_net_irr = (1 + baseline_irr) * 0.9680 - 1

    # Adjust modeled cash flows for fee payment at exit
    modeled_cf_with_fees_orig = modeled_cf_orig + [(exit_date, -total_fees)]
    
    # Aggregate cash flows by date for accurate IRR calculation
    modeled_cf_agg = aggregate_cashflows_by_date(modeled_cf_orig)
    modeled_cf_with_fees_agg = aggregate_cashflows_by_date(modeled_cf_with_fees_orig)

    # Compute IRRs
    modeled_irr = xirr(modeled_cf_agg)
    irr_with_fees = xirr(modeled_cf_with_fees_agg)

    # Enhanced Waterfall values with dynamic capital call fees (no PR on mgmt fees)
    premium = exit_val - book_val
    net_val = exit_val - total_fees
    
    # Create enhanced waterfall with individual fee breakdown including dynamic capital calls
    if (capital_call_fees > 0 or capital_call_pr > 0):
        labels_fig1 = ["Book Value", "Exit Premium", "Mgmt Fees", 
                       "Capital Call Fees", "Capital Call PR (8%)", "Net Exit Value"]
        values_fig1 = [book_val, premium, 
                      -mgmt_fees_only,  # Management fees without PR
                      -capital_call_fees, -capital_call_pr, net_val]
        measures_fig1 = ["absolute", "relative", "relative", "relative", "relative", "total"]
        text_fig1 = [
            f"${book_val:,.0f}", 
            f"${premium:,.0f}", 
            f"${abs(mgmt_fees_only):,.0f}",
            f"${abs(capital_call_fees):,.0f}",
            f"${abs(capital_call_pr):,.0f}",
            f"${net_val:,.0f}"
        ]
    else:
        # Original waterfall without capital calls (and no PR on mgmt fees)
        labels_fig1 = ["Book Value", "Exit Premium", "Management Fees", "Net Exit Value"]
        values_fig1 = [book_val, premium, -mgmt_fees_only, net_val]
        measures_fig1 = ["absolute", "relative", "relative", "total"]
        text_fig1 = [
            f"${book_val:,.0f}", 
            f"${premium:,.0f}", 
            f"${abs(mgmt_fees_only):,.0f}",
            f"${net_val:,.0f}"
        ]

    fig1_plotly = go.Figure(go.Waterfall(
        name = f"{company} Waterfall",
        orientation = "v",
        measure = measures_fig1,
        x = labels_fig1,
        textposition = "outside",
        text = text_fig1,
        y = values_fig1,
        connector = {"line":{"color":"rgb(63, 63, 63)"}},
        increasing = {"marker":{"color":"#2ca02c"}}, 
        decreasing = {"marker":{"color":"#d62728"}}, 
        totals = {"marker":{"color":"#9467bd"}}      
    ))
    
    title_text = f"{company} Exit Value Waterfall"
    
    
    fig1_plotly.update_layout(
        title_text=title_text,
        yaxis_title="Dollars",
        showlegend=False,
        height=500, 
        yaxis_showgrid=True,
        autosize=True
    )

    # Display enhanced metrics with individual fee information
    st.subheader("\U0001F4CA IRR Impact Summary")
    
    col1, col2 = st.columns(2)
    
    with col1:
        if book_date:
            st.write(f"**Book Value (as of {book_date.strftime('%Y-%m-%d')}):** ${book_val:,.0f}")
        else:
            st.write(f"**Book Value (date not available):** ${book_val:,.0f}")
        st.write(f"**Modeled Exit Value ({exit_moic}x):** ${exit_val:,.0f}")
        
        if baseline_irr is not None:
            st.write(f"**Baseline IRR:** {baseline_irr:.2%}")
        else:
            st.write(f"**Baseline IRR:** N/A")

        if calculated_new_baseline_net_irr is not None:
            st.write(f"**Baseline Net IRR:** {calculated_new_baseline_net_irr:.2%}")
        else:
            st.write(f"**Baseline Net IRR:** N/A")
        st.write(f"**Company Quarterly Fee:** ${company_quarterly_fee:,.0f}")
        
    with col2:
        st.write(f"**Company's Initial Balance:** ${company_initial_balance:,.0f}")
        st.write(f"**New Mgmt Fees to Exit:** ${new_fees:,.0f}")
        
        if modeled_irr is not None:
            st.write(f"**Modeled IRR (No Fees):** {modeled_irr:.2%}")
        else:
            st.write(f"**Modeled IRR (No Fees):** N/A")

        if irr_with_fees is not None:
            st.write(f"**Modeled IRR (With Fees):** {irr_with_fees:.2%}")
        else:
            st.write(f"**Modeled IRR (With Fees):** N/A")
    
    # Show enhanced fee details breakdown with dynamic capital calls
    st.subheader("Fee Calculation Details")
    
    days_to_exit = (exit_date - fee_date).days
    
    fee_breakdown_df = pd.DataFrame({
        "Component": [
            "Company Quarterly Fee", 
            "Company's Initial Balance Share", 
            "New Mgmt Fees Accrued", 
            "Total Management Fees (No PR)", 
            "Capital Call Fees (Company Share)",
            "Capital Call PR (8% Dynamic)",
            "Total Company Fees Due"
        ],
        "Amount": [
            company_quarterly_fee,
            company_initial_balance, 
            new_fees, 
            mgmt_fees_only,  # Management fees without PR
            capital_call_fees,
            capital_call_pr,
            total_fees
        ],
        "Calculation": [
            f"From Excel: ${company_quarterly_fee:,.2f} quarterly",
            f"({company_proportion:.4%} × ${fee_balance:,.0f})",
            f"{days_to_exit} days × (${company_quarterly_fee:,.0f} ÷ 91.25 days/qtr)",
            "Initial Share + New Fees (No PR)",
            f"Static principal × {company_proportion:.4%}",
            f"Dynamic: 8% from call dates to {exit_date.strftime('%Y-%m-%d')}",
            "Mgmt Fees + Capital Calls + Dynamic Capital Call PR"
        ]
    })
    
    # Show dynamic calculation details
    st.info(f"""
    **Dynamic Capital Call Priority Return:**
    • Principal: $289.5M (static from Excel)
    • Your exit date: {exit_date.strftime('%Y-%m-%d')} 
    • Company's share: {company_proportion:.2%}
    • Priority return calculated dynamically from individual call dates to your exit date
    • Formula: Amount × (1.08^(days_from_call_to_exit/365) - 1)
    """)
    
    # Add explanation
    st.info(f"""
    **Fee Calculation Method:**
    • {company}'s quarterly fee: ${company_quarterly_fee:,.0f} (from Excel)
    • Company's share of fund: {company_proportion:.2%}
    • Management fees have NO priority return
    • Capital call fees are allocated proportionally
    • Priority return (8%) calculated DYNAMICALLY to your exit date
    """)
    
    st.dataframe(fee_breakdown_df.style.format({"Amount": "${:,.0f}"}), height=300)
    
    st.plotly_chart(fig1_plotly, use_container_width=True)

    # MOIC Metrics (unchanged)
    net_investment = 0
    for _, row_data in cdata.iterrows():
        if row_data["Contributions"] < 0: 
            net_investment += -row_data["Contributions"] 
        elif row_data["Contributions"] > 0: 
            net_investment -= row_data["Contributions"] 
    
    net_investment = max(0, net_investment)
    
    baseline_moic_val = book_val / net_investment if net_investment > 0 else None
    modeled_moic_val = exit_val / net_investment if net_investment > 0 else None
    delta_moic_val = modeled_moic_val - baseline_moic_val if baseline_moic_val and modeled_moic_val else None

    st.subheader("🔢 MOIC Summary")
    st.write(f"**Net Investment:** ${net_investment:,.0f}")
    if baseline_moic_val:
        st.write(f"**Baseline MOIC:** {baseline_moic_val:.2f}x")
        st.write(f"**Modeled MOIC:** {modeled_moic_val:.2f}x")
        st.write(f"**Δ MOIC:** {delta_moic_val:+.2f}x")
    else:
        st.write("**MOIC metrics not available - no net investment recorded**")

    # IRR Sensitivity with dynamic capital calls
    sensitivity_title = f"📈 IRR Sensitivity by Exit MOIC (with Dynamic Capital Call Fees)"
    
    st.subheader(sensitivity_title)
    show_target = st.checkbox(f"Show {target_irr_param:.1f}% Target IRR", True, key="tab1_show_target_checkbox")
    show_fund_line = st.checkbox("Show Fund IRR Line", True)
    moics_range = [x / 10 for x in range(5, 101, 5)]
    irr_series = []
    fund_irr_series = []
    
    for m_iter in moics_range: 
        val = book_val * m_iter
        test_cf_orig = flows + [(exit_date, val)]
        
        # Calculate fees with dynamic capital calls for this MOIC
        test_fee_details = calculate_company_management_fees_with_hurdle(
            fee_date, exit_date, fee_balance, company, 
            hurdle_rate=0.08,
            is_first_exit=True
        )
        test_total_fees = test_fee_details['total_fees']
        
        test_cf_with_fees_orig = test_cf_orig + [(exit_date, -test_total_fees)]
        test_cf_with_fees_agg = aggregate_cashflows_by_date(test_cf_with_fees_orig)
        
        irr_val = xirr(test_cf_with_fees_agg)
        irr_series.append(irr_val * 100 if irr_val else None)

        if show_fund_line:
            fund_cf_list = []
            for comp_iter_2 in company_list: 
                comp_df_iter = data_df[data_df["Deal Name"] == comp_iter_2] 
                cash_list = [(r_item["Date"], r_item["Distributions"] + r_item["Contributions"]) 
                        for _, r_item in comp_df_iter.iterrows()]
                        
                bv_row_iter = comp_df_iter[comp_df_iter["Book Value"] > 0].sort_values("Date", ascending=False)
                
                if comp_iter_2 == company:
                    exit_with_fees = val - test_total_fees
                    cash_list.append((exit_date, exit_with_fees))
                elif not bv_row_iter.empty:
                    cash_list.append((bv_row_iter.iloc[0]["Date"], bv_row_iter.iloc[0]["Book Value"]))
                    
                fund_cf_list.extend(cash_list)
                
            fund_cf_agg = aggregate_cashflows_by_date(fund_cf_list)
            
            fund_irr_result = xirr(fund_cf_agg)
            fund_irr_series.append(fund_irr_result * 100 if fund_irr_result else None)

    fig2_plotly = go.Figure()
    fig2_plotly.add_trace(go.Scatter(x=moics_range, y=irr_series, mode='lines', name="Company IRR", line=dict(color="blue")))

    if show_fund_line:
        fig2_plotly.add_trace(go.Scatter(x=moics_range, y=fund_irr_series, mode='lines', name="Fund IRR", line=dict(color="orange")))

    if show_target:
        fig2_plotly.add_hline(y=target_irr_param, line_dash="dash", line_color="red",
                              annotation_text=f"Target IRR ({target_irr_param:.1f}%)",
                              annotation_position="bottom right")

    fig2_plotly.update_layout(
        title_text=sensitivity_title,
        xaxis_title="Exit MOIC",
        yaxis_title="IRR (%)",
        legend_title_text="Legend",
        height=450, 
        showlegend=True,
        autosize=True,
        xaxis_showgrid=True,
        yaxis_showgrid=True
    )
    st.plotly_chart(fig2_plotly, use_container_width=True)

    # IRR Over Time with dynamic capital calls
    time_title = f"📅 IRR Over Time Until Exit (with Dynamic Capital Call Fees)"
    
    st.subheader(time_title)
    current_date = datetime.today()
    
    # Ensure exit_date is timezone-naive if current_date is, or make them compatible
    if exit_date.tzinfo is not None and current_date.tzinfo is None:
        current_date = pd.Timestamp(current_date).tz_localize(exit_date.tzinfo)
    elif exit_date.tzinfo is None and current_date.tzinfo is not None:
        exit_date = pd.Timestamp(exit_date).tz_localize(current_date.tzinfo)

    # Ensure start date is not after end date for date_range
    if current_date <= exit_date:
        date_range_pd = pd.date_range(start=current_date, end=exit_date, freq="QS")
    else:
        date_range_pd = pd.to_datetime([])

    time_series = {}
    fee_time_series = {}  # Track fees over time
    
    for dt_iter in date_range_pd: 
        # Calculate fees with dynamic capital calls for each date
        tmp_fee_details = calculate_company_management_fees_with_hurdle(
            fee_date, dt_iter, fee_balance, company, 
            hurdle_rate=0.08,
            is_first_exit=True
        )
        tmp_total_fees = tmp_fee_details['total_fees']
        
        ts_cf_orig = flows + [(dt_iter, exit_val - tmp_total_fees)]
        ts_cf_agg = aggregate_cashflows_by_date(ts_cf_orig)
        
        irr_val_ts = xirr(ts_cf_agg)
        if irr_val_ts:
            time_series[dt_iter.date()] = irr_val_ts * 100
            fee_time_series[dt_iter.date()] = tmp_total_fees

    if time_series:
        # Create subplot with IRR and fees
        fig3_plotly = make_subplots(
            rows=2, cols=1,
            subplot_titles=("IRR Progression", "Total Fees Due"),
            vertical_spacing=0.12,
            row_heights=[0.7, 0.3]
        )
        
        dates_fig3 = list(time_series.keys())
        irr_values_fig3 = list(time_series.values())
        fee_values_fig3 = [fee_time_series.get(d, 0) for d in dates_fig3]
        
        # IRR subplot
        fig3_plotly.add_trace(
            go.Scatter(
                x=dates_fig3,
                y=irr_values_fig3,
                mode='lines+markers',
                name="IRR",
                marker=dict(color="purple"),
                line=dict(color="purple")
            ),
            row=1, col=1
        )
        
        # Fees subplot
        fig3_plotly.add_trace(
            go.Scatter(
                x=dates_fig3,
                y=fee_values_fig3,
                mode='lines+markers',
                name="Total Fees",
                marker=dict(color="red"),
                line=dict(color="red"),
                fill='tozeroy',
                fillcolor='rgba(255,0,0,0.1)'
            ),
            row=2, col=1
        )
        
        fig3_plotly.update_xaxes(title_text="Hypothetical Exit Date", row=2, col=1)
        fig3_plotly.update_yaxes(title_text="IRR (%)", row=1, col=1)
        fig3_plotly.update_yaxes(title_text="Fees ($)", row=2, col=1)
        
        fig3_plotly.update_layout(
            title_text=f"IRR Progression Over Time ({company})",
            height=700,
            showlegend=False,
            autosize=True,
            xaxis_tickformat='%Y-%m-%d',
            xaxis2_tickformat='%Y-%m-%d',
            xaxis_tickangle=-30,
            xaxis2_tickangle=-30
        )
        
        st.plotly_chart(fig3_plotly, use_container_width=True)
        
        # Show impact of dynamic priority return over time
        st.subheader("🎯 Dynamic Priority Return Impact Analysis")
        
        if fee_time_series:
            # Calculate priority return portion at different dates
            priority_impact_data = []
            
            for dt in dates_fig3[-5:]:  # Show last 5 dates
                dt_timestamp = pd.Timestamp(dt)
                fee_details_impact = calculate_company_management_fees_with_hurdle(
                    fee_date, dt_timestamp, fee_balance, company, 
                    hurdle_rate=0.08,
                    is_first_exit=True
                )
                
                priority_impact_data.append({
                    "Exit Date": dt,
                    "Days from Balance": (dt_timestamp - fee_date).days,
                    "Management Fees": fee_details_impact.get('mgmt_fees_only', 0),
                    "Capital Call Fees": fee_details_impact.get('capital_call_fees', 0),
                    "Capital Call PR (Dynamic)": fee_details_impact.get('capital_call_pr', 0),
                    "Total Fees": fee_details_impact['total_fees'],
                    "PR as % of Total": (fee_details_impact.get('capital_call_pr', 0) / fee_details_impact['total_fees'] * 100) if fee_details_impact['total_fees'] > 0 else 0
                })
            
            impact_df = pd.DataFrame(priority_impact_data)
            
            st.write("**Dynamic Priority Return Growth Over Time:**")
            st.dataframe(impact_df.style.format({
                "Exit Date": "{:%Y-%m-%d}",
                "Days from Balance": "{:,.0f}",
                "Management Fees": "${:,.0f}",
                "Capital Call Fees": "${:,.0f}",
                "Capital Call PR (Dynamic)": "${:,.0f}",
                "Total Fees": "${:,.0f}",
                "PR as % of Total": "{:.1f}%"
            }))
            
            st.info("💡 **Key Insight**: Priority return on capital calls increases over time, but management fees have NO priority return. Later exits result in higher total fees due to compounding capital call priority returns.")
    
    else:
        st.info("No future dates available for IRR progression analysis.")
    
    # Add comparison with and without dynamic capital calls
    st.subheader("💰 Impact of Dynamic Capital Call Fees")
    
    # Calculate fees without capital calls for comparison
    fee_details_no_cc = calculate_company_management_fees_with_hurdle(
        fee_date, exit_date, fee_balance, company, 
        hurdle_rate=0.08,
        is_first_exit=True
    )
    fees_without_capital_calls = fee_details_no_cc['total_fees']
    
    # Create comparison
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric(
            "Fees Without Capital Calls", 
            f"${fees_without_capital_calls:,.0f}"
        )
    
    with col2:
        st.metric(
            "Fees With Dynamic Capital Calls", 
            f"${total_fees:,.0f}",
            delta=f"+${total_fees - fees_without_capital_calls:,.0f}"
        )
    
    with col3:
        capital_call_impact_pct = ((total_fees / fees_without_capital_calls) - 1) * 100 if fees_without_capital_calls > 0 else 0
        st.metric(
            "Capital Call Impact", 
            f"{capital_call_impact_pct:.1f}%",
            delta=f"+{capital_call_impact_pct:.1f}%"
        )
    
    st.info(f"🎯 **Dynamic Calculation**: Capital call priority return varies based on your exit date ({exit_date.strftime('%Y-%m-%d')}). Earlier exits = lower PR, later exits = higher PR.")
