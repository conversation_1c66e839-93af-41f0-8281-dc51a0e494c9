# app.py
# Updated version with actual capital call fee integration ($389M total)

import streamlit as st
import pandas as pd
import matplotlib.pyplot as plt
import plotly.graph_objects as go
import numpy as np
from datetime import datetime, timedelta
from math import pow
from collections import OrderedDict, defaultdict
import io
import base64

from data_loader import load_data, validate_company_fees, COMPANY_QUARTERLY_FEES
from financial_calculations import calculate_management_fees_with_hurdle, xirr, aggregate_cashflows_by_date, calculate_fund_moic, calculate_gp_carry
from tab_company_analysis import display_company_analysis_tab
from tab_fund_analysis import display_fund_analysis_tab
from excel_export_helpers import get_company_analysis_data, get_fund_analysis_data
from enhanced_ui import apply_enhanced_styling
from pdf_export_helpers import generate_pdf

# Use SQLite template manager for persistent storage
from template_manager_sqlite import sqlite_template_manager
template_manager = sqlite_template_manager

st.set_page_config(layout="wide", initial_sidebar_state="expanded")

# Apply enhanced UI styling
apply_enhanced_styling()

def load_css(file_name):
    with open(file_name) as f:
        st.markdown(f'<style>{f.read()}</style>', unsafe_allow_html=True)

load_css('style.css')

# Add JavaScript to continuously enforce width constraints
st.markdown("""
<script>
function enforceWidthConstraints() {
    // Set overflow hidden on body and html
    document.documentElement.style.overflowX = 'hidden';
    document.body.style.overflowX = 'hidden';
    document.body.style.maxWidth = '100vw';
    
    // Target all potentially problematic elements
    const selectors = [
        '[data-testid="stPlotlyChart"]',
        '[data-testid="stDataFrame"]',
        '[data-testid="stTable"]',
        '.js-plotly-plot',
        '.plotly',
        '.plotly-graph-div',
        '.main .block-container',
        '.element-container',
        'div[data-testid="stHorizontalBlock"]',
        'div[data-testid="column"]'
    ];
    
    selectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => {
            el.style.maxWidth = '100%';
            el.style.overflowX = 'hidden';
            el.style.boxSizing = 'border-box';
        });
    });
    
    // Special handling for tables
    const tables = document.querySelectorAll('table, .dataframe');
    tables.forEach(table => {
        table.style.width = '100%';
        table.style.maxWidth = '100%';
        table.style.tableLayout = 'auto';
        table.style.wordWrap = 'break-word';
    });
    
    // Force plotly to resize
    const plotlyCharts = document.querySelectorAll('[data-testid="stPlotlyChart"] .js-plotly-plot');
    plotlyCharts.forEach(chart => {
        if (window.Plotly && chart._fullLayout) {
            const container = chart.closest('[data-testid="stPlotlyChart"]');
            if (container) {
                const containerWidth = container.clientWidth;
                window.Plotly.relayout(chart, {width: containerWidth});
            }
        }
    });
}

// Run immediately
enforceWidthConstraints();

// Run after a short delay to catch dynamically loaded content
setTimeout(enforceWidthConstraints, 100);
setTimeout(enforceWidthConstraints, 500);
setTimeout(enforceWidthConstraints, 1000);

// Set up observer for dynamic content
if (window.MutationObserver) {
    const observer = new MutationObserver(function(mutations) {
        let shouldEnforce = false;
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                shouldEnforce = true;
            }
        });
        if (shouldEnforce) {
            setTimeout(enforceWidthConstraints, 50);
        }
    });
    
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
}

// Also run on window resize
window.addEventListener('resize', function() {
    setTimeout(enforceWidthConstraints, 100);
});
</script>
""", unsafe_allow_html=True)

# CSS styling is now handled by style.css

# Display title
st.title("Veritas Fund Exit IRR Modeler")

# Display Veritas Logo in sidebar
with st.sidebar:
    st.image("Veritas Logo.jpg", width=75)

# Load data at the top level so it's available to both tabs
try:
    loaded_data_frames = load_data()
    data = loaded_data_frames["gross_flows"]  # This is df_gross_flows, used by existing script logic
    df_net_cash_flows = loaded_data_frames["net_cash_flows"]
    df_terminal_book_values = loaded_data_frames["terminal_book_values"]
    company_fees = loaded_data_frames["company_fees"]
    fee_mapping = loaded_data_frames["fee_mapping"]
    call_dates = loaded_data_frames["call_dates"]
    investment_pr_amounts = loaded_data_frames["investment_pr_amounts"]
    
    # Ensure 'companies' list is derived from the main data source used for the dropdown
    companies = sorted(data["Deal Name"].unique()) if not data.empty else []
    
    # Display fee loading status
    if company_fees and validate_company_fees(company_fees):
        pass
    else:
        st.sidebar.warning("⚠️ Using default company fees")
        company_fees = COMPANY_QUARTERLY_FEES.copy()
    
    
    
except Exception as e:
    st.error(f"Error loading data: {str(e)}")
    st.stop()

# st.sidebar.header("Model Parameters")

# Handle template loading
default_target_irr = 20.0
default_gp_commitment = 92844859
default_mgmt_fee_balance = 37644819
default_mgmt_fee_date = datetime(2025, 3, 31)
default_quarterly_fee = None  # Will be calculated from company fees

if 'pending_template_load' in st.session_state:
    template_data = st.session_state.pending_template_load
    params = template_data.get('parameters', {})
    
    # Update defaults from template
    default_target_irr = params.get('target_irr', default_target_irr)
    default_gp_commitment = params.get('gp_commitment', default_gp_commitment)
    default_mgmt_fee_balance = params.get('mgmt_fee_balance', default_mgmt_fee_balance)
    default_mgmt_fee_date = params.get('mgmt_fee_date', default_mgmt_fee_date)
    default_quarterly_fee = params.get('quarterly_fee', default_quarterly_fee)
    
    # Store company scenarios for fund analysis tab
    st.session_state.template_company_scenarios = template_data.get('company_scenarios', [])
    
    # IMPORTANT: Clear cached data so template values can be applied
    if 'fund_analysis_edited_df' in st.session_state:
        del st.session_state.fund_analysis_edited_df
    if 'plan_editor_data' in st.session_state:
        del st.session_state.plan_editor_data
    if 'excel_export_cache' in st.session_state:
        st.session_state.excel_export_cache.clear()
    
    # Clear any other cached data that might interfere
    cache_keys_to_clear = [k for k in st.session_state.keys() if 'cache' in k.lower() or 'fund_analysis' in k.lower()]
    for key in cache_keys_to_clear:
        if key in st.session_state:
            del st.session_state[key]
    
    # Clear the pending load
    del st.session_state.pending_template_load
    
    st.sidebar.success(f"✅ Loaded template: {template_data.get('template_name', 'Unknown')}")

# Set default values directly instead of showing input fields
target_irr = default_target_irr
gp_commitment = default_gp_commitment

# target_irr = st.sidebar.number_input("Target IRR (%)", value=default_target_irr, key="main_target_irr",
#                                      help="Target Internal Rate of Return for comparison. This appears as a reference line on IRR charts to show how the fund's projected performance compares to the target.")
# gp_commitment = st.sidebar.number_input("GP Commitment ($)", value=default_gp_commitment, step=10000, 
#                                        help="GP commitment amount that is deducted from total fund investments. "
#                                             "Reduces the denominator in MOIC and hurdle return calculations since GP capital is not subject to management fees or carry.")

# Capital call fees are always included
include_capital_calls = True

# Show actual capital call amounts if enabled
# if include_capital_calls:
#     with st.sidebar.expander("💰 Capital Call Fees", expanded=False):
#         st.write("**From Excel 'Fees and Expense PR' tab:**")
#         st.metric("Principal (Static)", "$289,537,026")
#         st.write("**Priority Return (8%):** *Calculated dynamically to exit date*")
      

# Set default values directly instead of showing input fields
mgmt_fee_balance = default_mgmt_fee_balance
mgmt_fee_date = default_mgmt_fee_date

# Calculate quarterly fee from loaded company fees
if company_fees:
    calculated_quarterly_fee = sum(company_fees.values())
    # Use template value if provided, otherwise use calculated
    if default_quarterly_fee is not None:
        quarterly_fee = default_quarterly_fee
    else:
        quarterly_fee = calculated_quarterly_fee
else:
    quarterly_fee = default_quarterly_fee if default_quarterly_fee is not None else 19194832

# # Add management fee parameters in the sidebar
# with st.sidebar.expander("Fund Management Fee Parameters", expanded=False):
#     mgmt_fee_balance = st.number_input("Management Fee Balance ($)", value=default_mgmt_fee_balance, step=10000,
#                                       help="Outstanding management fee balance as of the balance date. "
#                                            "This accrues at the quarterly fee rate until fees are paid at company exits.")
#     mgmt_fee_date = st.date_input("Date of Balance", default_mgmt_fee_date,
#                                  help="Date when the management fee balance was calculated. "
#                                       "Used as the starting point for accruing additional management fees to exit dates.")

#     # Calculate total quarterly fee from loaded company fees or use default
#     if company_fees:
#         calculated_quarterly_fee = sum(company_fees.values())
#         # Use template value if provided, otherwise use calculated
#         if default_quarterly_fee is not None:
#             quarterly_fee_value = default_quarterly_fee
#         else:
#             quarterly_fee_value = calculated_quarterly_fee
            
#         quarterly_fee = st.number_input(
#             "Total Quarterly Gross Fees ($)",
#             value=quarterly_fee_value,
#             step=10000.0,
#             help="Total quarterly management fees across all portfolio companies. "
#                  "Used to calculate fee accrual from the balance date to each company's exit date. "
#                  "Individual company fees are deducted as companies exit to reduce ongoing fee burden."
#         )
#     else:
#         quarterly_fee_value = default_quarterly_fee if default_quarterly_fee is not None else 19194832
#         quarterly_fee = st.number_input("Quarterly Gross Fees ($)", value=quarterly_fee_value, step=10000,
#                                        help="Total quarterly management fees across all portfolio companies. "
#                                             "Used to calculate fee accrual from the balance date to each company's exit date.")
    
#     # Add note about priority returns
#     if include_capital_calls:
#         st.info("💡 Priority returns (8% hurdle) apply ONLY to capital call fees, not management fees")
#     else:
#         st.info("💡 No priority returns on management fees")

# # Display company fee reference
# with st.sidebar.expander("Portfolio Company Quarterly Fees", expanded=False):
#     st.write("**Individual Company Fees (from Excel):**")
#     for company, fee in COMPANY_QUARTERLY_FEES.items():
#         st.write(f"• {company}: ${fee:,.0f}")
#     st.write(f"**Total: ${sum(COMPANY_QUARTERLY_FEES.values()):,.0f}**")
#     st.info("These are used instead of proportional fund-level fees")

# SQLite Template Management Section
st.sidebar.divider()
st.sidebar.header("📁 Templates")



# Load Template
with st.sidebar.expander("📂 Load Template", expanded=False):
    available_templates = template_manager.list_templates()
    
    if available_templates:
        # Filter by category
        template_stats = template_manager.get_template_statistics()
        categories = list(template_stats.get('categories', {}).keys())
        
        if categories:
            show_category = st.selectbox(
                "Filter by category:",
                options=["All"] + categories,
                key="template_filter_category"
            )
            
            if show_category != "All":
                filtered_templates = [t for t in available_templates 
                                    if template_manager.get_template_details(t).get('category') == show_category]
            else:
                filtered_templates = available_templates
        else:
            filtered_templates = available_templates
        
        if filtered_templates:
            final_selected_template = st.selectbox(
                "Select template:",
                options=[""] + filtered_templates,
                format_func=lambda x: "Choose a template..." if x == "" else x,
                key="template_selector"
            )
            
            if final_selected_template:
                template_details = template_manager.get_template_details(final_selected_template)
                if template_details:
                    st.write("**Template Details:**")
                    st.caption(f"📅 {template_details['created']}")
                    st.caption(f"🏢 {template_details['num_companies']} companies")
                    
                        
                        
                        
                
                if st.button("Load Template", type="primary", key="load_template_btn"):
                    template_data = template_manager.load_template(final_selected_template)
                    if template_data:
                        st.session_state.pending_template_load = template_data
                        st.success(f"Loading {final_selected_template}...")
                        st.rerun()
        else:
            st.info("No templates match your filter")
    else:
        st.info("No templates saved yet")

# Save Template
with st.sidebar.expander("💾 Save Template", expanded=False):
    new_template_name = st.text_input(
        "Template name:",
        placeholder="Enter descriptive name",
        key="new_template_name"
    )
    
    template_category = st.selectbox(
        "Category:",
        options=["Veritas Capital Fund VII", "Custom"],
        key="template_category"
    )
    
    template_tags = st.text_input(
        "Tags (comma-separated):",
        placeholder="e.g., Q1, high-growth, exit-plan",
        key="template_tags"
    )
    
    st.write("")  # Add spacing before button
    if st.button("💾 Save Template", type="primary", key="save_template_btn", disabled=not new_template_name):
        try:
            # Process tags
            tags_list = [tag.strip() for tag in template_tags.split(',') if tag.strip()] if template_tags else []
            
            # Collect current parameters
            save_parameters = {
                'target_irr': target_irr,
                'gp_commitment': gp_commitment,
                'mgmt_fee_balance': mgmt_fee_balance,
                'mgmt_fee_date': mgmt_fee_date,
                'quarterly_fee': quarterly_fee
            }
            
            # Get company scenarios
            save_company_scenarios = []
            
            # First try to get values from the current plan editor session state
            if 'plan_editor_data' in st.session_state:
                plan_editor_data = st.session_state.plan_editor_data
                
                for company in companies:
                    company_key = company.replace(' ', '_').replace('.', '_')
                    moic_key = f"moic_{company_key}"
                    date_key = f"exit_date_{company_key}"
                    
                    # Get MOIC value from session state or default
                    moic_value = plan_editor_data.get(moic_key, 2.0)
                    
                    # Get Exit Date value from session state or default
                    exit_date_value = plan_editor_data.get(date_key)
                    if exit_date_value is None:
                        # Default to 12 months from latest data date
                        latest_data_date = data["Date"].max()
                        exit_date_value = (pd.to_datetime(latest_data_date) + pd.DateOffset(months=12)).date()
                    
                    save_company_scenarios.append({
                        'company': company,
                        'moic': float(moic_value),
                        'exit_date': exit_date_value
                    })
            
            # Fallback to fund_analysis_edited_df if available
            elif 'fund_analysis_edited_df' in st.session_state:
                edited_df = st.session_state.fund_analysis_edited_df
                
                for _, row in edited_df.iterrows():
                    save_company_scenarios.append({
                        'company': row['Company'],
                        'moic': float(row['MOIC']),
                        'exit_date': row['Exit Date']
                    })
            
            # Final fallback to default scenarios
            else:
                st.warning("⚠️ No edited data found, using default values. Please edit MOIC and Exit Date values in the Fund Analysis section first.")
                
                latest_data_date = data["Date"].max()
                default_exit_date = (pd.to_datetime(latest_data_date) + pd.DateOffset(months=12)).date()
                
                for company in companies:
                    comp_data = data[data["Deal Name"] == company]
                    book_row = comp_data[comp_data["Book Value"] > 0].sort_values("Date", ascending=False)
                    
                    if not book_row.empty:
                        default_moic = 2.0
                    else:
                        net_investment = 0
                        for _, row in comp_data.iterrows():
                            if row["Contributions"] < 0: 
                                net_investment += -row["Contributions"] 
                            elif row["Contributions"] > 0: 
                                net_investment -= row["Contributions"]
                        default_moic = 0.0 if net_investment > 0 else 2.0
                    
                    save_company_scenarios.append({
                        'company': company,
                        'moic': default_moic,
                        'exit_date': default_exit_date
                    })
            
            # Save the template
            if template_manager.save_template(new_template_name, save_parameters, save_company_scenarios, template_category, tags_list):
                st.success(f"✅ Template '{new_template_name}' saved successfully!")
                st.success(f"📊 Saved {len(save_company_scenarios)} company scenarios with MOIC and Exit Date values")
                st.rerun()
            else:
                st.error("Failed to save template")
                
        except Exception as e:
            st.error(f"Error saving template: {str(e)}")

# Template Management
with st.sidebar.expander("🔧 Manage Templates", expanded=False):
    available_templates = template_manager.list_templates()
    
    if available_templates:
        template_to_manage = st.selectbox(
            "Select template:",
            options=available_templates,
            key="template_to_manage"
        )
        
        if st.button("📤 Export", key="export_template_btn"):
            json_data = template_manager.export_template(template_to_manage)
            if json_data:
                st.download_button(
                    label="📁 Download",
                    data=json_data,
                    file_name=f"{template_to_manage}_export.json",
                    mime="application/json",
                    key="download_template_btn"
                )
        
        if st.button("📋 Duplicate", key="duplicate_template_btn"):
            template_data = template_manager.load_template(template_to_manage)
            if template_data:
                new_name = f"{template_to_manage}_copy"
                params = template_data.get('parameters', {})
                scenarios = template_data.get('company_scenarios', [])
                
                if template_manager.save_template(new_name, params, scenarios):
                    st.success(f"Duplicated as {new_name}")
                    st.rerun()
        
        if st.button("🗑️ Delete", key="delete_template_btn", type="secondary"):
            if template_manager.delete_template(template_to_manage):
                st.success(f"Deleted: {template_to_manage}")
                st.rerun()


# -----------------------------------------------------------------
# 📄 PDF Export of Fund Analysis Tables (Exit Plan & Summary Metrics)
# -----------------------------------------------------------------
with st.sidebar.expander("📄 Export Tables to PDF", expanded=False):
    edited_df_for_pdf = st.session_state.get("fund_analysis_edited_df")
    summary_metrics_for_pdf = st.session_state.get("fund_summary_metrics")
    waterfall_b64_for_pdf = st.session_state.get("fund_waterfall_chart_b64") # PNG
    waterfall_svg_b64_for_pdf = st.session_state.get("fund_waterfall_chart_svg_b64") # SVG

    if edited_df_for_pdf is None or edited_df_for_pdf.empty:
        st.info("Run the fund analysis first to enable PDF export.")
        st.button("Generate PDF", key="generate_fund_pdf_btn_disabled", disabled=True)
    else:
        try:
            # Debug: Log what chart data is available
            print(f"DEBUG PDF Export: SVG data available: {waterfall_svg_b64_for_pdf is not None}")
            print(f"DEBUG PDF Export: PNG data available: {waterfall_b64_for_pdf is not None}")
            if waterfall_svg_b64_for_pdf:
                print(f"DEBUG PDF Export: SVG data length: {len(waterfall_svg_b64_for_pdf)} chars")
            if waterfall_b64_for_pdf:
                print(f"DEBUG PDF Export: PNG data length: {len(waterfall_b64_for_pdf)} chars")
            
            # Prefer SVG if available, otherwise fall back to PNG
            chart_data_for_pdf = waterfall_svg_b64_for_pdf if waterfall_svg_b64_for_pdf else waterfall_b64_for_pdf
            chart_type_for_pdf = "svg" if waterfall_svg_b64_for_pdf else "png"
            
            # If no chart data is available but we have the figure object, try to generate it now
            if not chart_data_for_pdf and "fund_waterfall_figure" in st.session_state:
                # TEMPORARILY DISABLED to prevent hanging
                print("DEBUG: Chart regeneration skipped to prevent hanging")
                chart_data_for_pdf = None
                chart_type_for_pdf = "png"

            # Load and encode the logo
            with open("Veritas Logo.jpg", "rb") as f:
                logo_b64 = base64.b64encode(f.read()).decode()

            # Debug: Show what's being passed to PDF generation
            if not chart_data_for_pdf:
                st.warning("⚠️ No chart data available for PDF export. Chart will be missing from PDF.")
            else:
                st.info(f"📊 Chart data ready for PDF export ({chart_type_for_pdf} format)")

            pdf_bytes = generate_pdf(
                edited_df_for_pdf,
                summary_metrics_for_pdf,
                chart_data_for_pdf, # Pass the chosen chart data
                chart_type=chart_type_for_pdf, # Pass the type
                logo_b64=logo_b64,
            )
            
            # Direct download button
            st.write("")  # Add vertical spacing
            st.download_button(
                label="📥 Generate PDF",
                data=pdf_bytes,
                file_name="fund_analysis_tables.pdf",
                mime="application/pdf",
                key="generate_fund_pdf_btn"
            )
        except Exception as e:
            st.error(f"PDF generation failed: {e}")
            st.button("Generate PDF", key="generate_fund_pdf_btn_error", disabled=True)

# Convert date input to datetime
mgmt_fee_date = pd.to_datetime(mgmt_fee_date)

# Function to generate Excel export with caching and optimizations
def generate_excel_output():
    # Initialize cache if not exists
    if 'excel_export_cache' not in st.session_state:
        st.session_state.excel_export_cache = {}
    
    # Get current inputs
    company_list = companies
    fee_balance = mgmt_fee_balance
    fee_date = mgmt_fee_date
    q_fee = quarterly_fee
    target_irr_param = target_irr

    # For company tab inputs, use values from session_state if available
    company = st.session_state.get('tab1_company_selectbox', company_list[0] if company_list else None)
    exit_moic = st.session_state.get('tab1_exit_moic_slider', 3.0)
    exit_date_input = st.session_state.get('tab1_exit_date_input', datetime(2026, 5, 19))
    exit_date = pd.to_datetime(exit_date_input)
    
    # Cache key for company analysis
    company_cache_key = f"company_analysis_{company}_{exit_moic}_{exit_date}_{include_capital_calls}"
    
    # Get company analysis data with caching
    if company:
        if company_cache_key in st.session_state.excel_export_cache:
            company_analysis_data = st.session_state.excel_export_cache[company_cache_key]
            company_analysis_data_available = bool(company_analysis_data[0])
        else:
            company_analysis_data = get_company_analysis_data(
                data, company, exit_moic, exit_date, fee_balance, fee_date, q_fee, 
                target_irr_param, company_list, include_capital_calls
            )
            st.session_state.excel_export_cache[company_cache_key] = company_analysis_data
            company_analysis_data_available = bool(company_analysis_data[0])
    else:
        company_analysis_data = [None]*5
        company_analysis_data_available = False

    # Cache key for fund analysis
    fund_cache_key = f"fund_analysis_{fee_balance}_{fee_date}_{q_fee}_{include_capital_calls}_{gp_commitment}"
    
    # Get fund analysis data with caching
    if fund_cache_key in st.session_state.excel_export_cache:
        fund_analysis_data = st.session_state.excel_export_cache[fund_cache_key]
    else:
        # For fund tab, use edited_df from session_state if available
        if 'fund_analysis_edited_df' in st.session_state:
            edited_df = st.session_state.fund_analysis_edited_df
        else:
            # Fallback: create a minimal default plan_df
            st.warning("Fund analysis data might be based on defaults as the tab may not have been visited yet.")
            company_moics_default = {}
            company_book_values_default = {}
            for comp_iter_default in company_list:
                comp_data_iter_default = data[data["Deal Name"] == comp_iter_default]
                book_row_iter_default = comp_data_iter_default[comp_data_iter_default["Book Value"] > 0].sort_values("Date", ascending=False)
                if not book_row_iter_default.empty:
                    company_moics_default[comp_iter_default] = 2.0
                    company_book_values_default[comp_iter_default] = book_row_iter_default.iloc[0]["Book Value"]
                else:
                    company_moics_default[comp_iter_default] = 0.0
            
            latest_data_date = data["Date"].max()
            default_exit_date_for_plan = (pd.to_datetime(latest_data_date) + pd.DateOffset(months=12)).date()

            edited_df = pd.DataFrame({
                "Company": company_list,
                "MOIC": [company_moics_default.get(c, 2.0) for c in company_list],
                "Exit Date": [default_exit_date_for_plan] * len(company_list)
            })

        fund_analysis_data = get_fund_analysis_data(
            data, edited_df, fee_balance, fee_date, q_fee, 
            target_irr_param, company_list, include_capital_calls, company_fees, fee_mapping, gp_commitment, call_dates, investment_pr_amounts
        )
        st.session_state.excel_export_cache[fund_cache_key] = fund_analysis_data

    # Unpack fund analysis data
    fund_summary_metrics, edited_df_processed, years_summary, fee_timeline_df, fee_df_display, irr_df_prog, sens_df = fund_analysis_data

    # Create Excel in memory with optimized writing
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        # Prepare all dataframes to write in a single pass
        dataframes_to_write = []
        
        # Company Analysis sheet
        if company_analysis_data_available:
            company_summary_metrics, fee_breakdown_df, impact_df, _, _ = company_analysis_data
            summary_df = pd.DataFrame(list(company_summary_metrics.items()), columns=['Metric', 'Value'])
            
            capital_call_note = 'with actual capital call fees ($389M total) at first exit' if include_capital_calls else 'without capital call fees'
            note_df = pd.DataFrame([{
                "Note": f"This analysis uses individual company fees {capital_call_note}"
            }])
            
            dataframes_to_write.extend([
                ('Company Analysis', summary_df, 0),
                ('Company Analysis', note_df, len(summary_df) + 2)
            ])
            
            if fee_breakdown_df is not None:
                dataframes_to_write.append(('Company Analysis', fee_breakdown_df, len(summary_df) + 5))
            if impact_df is not None:
                dataframes_to_write.append(('Company Analysis', impact_df, len(summary_df) + 8))
        else:
            placeholder_df = pd.DataFrame({"Message": ["Company analysis data not available"]})

    return output

# Display Fund Analysis directly (no tabs)
display_fund_analysis_tab(
    data_df=data,
    company_list=companies,
    fee_balance=mgmt_fee_balance,
    fee_date=mgmt_fee_date,
    q_fee=quarterly_fee,
    target_irr_val=target_irr,
    company_fees=company_fees,
    fee_mapping=fee_mapping,
    gp_commitment=gp_commitment,
    call_dates=call_dates,
    investment_pr_amounts=investment_pr_amounts
)

# Template saving is now handled directly in the button click above

# Layout management is now handled by CSS
