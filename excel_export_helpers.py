# excel_export_helpers.py
# Updated version with dynamic capital call fee support and IPO scenario export

import pandas as pd
from datetime import datetime
from financial_calculations import calculate_company_management_fees_with_hurdle, calculate_management_fees_with_hurdle, xirr, aggregate_cashflows_by_date, calculate_fund_moic, calculate_gp_carry, calculate_hurdle_return_breakdown
from ipo_scenario import create_ipo_distribution_df

def get_company_analysis_data(data_df, company, exit_moic, exit_date, fee_balance, fee_date, q_fee, target_irr_param, company_list, include_capital_calls=True):
    """
    Calculates and returns all data for the Company IRR + Fees + Waterfall tab using individual company fees.
    Updated with dynamic capital call fee support.
    """
    # Ensure exit_date is a datetime object
    exit_date = pd.to_datetime(exit_date)

    # Filter data
    cdata = data_df[data_df["Deal Name"] == company]
    flows = [(row["Date"], row["Distributions"] + row["Contributions"]) for _, row in cdata.iterrows()]
    book_row = cdata[cdata["Book Value"] > 0].sort_values("Date", ascending=False)
    
    if book_row.empty:
        # Return empty data if no book value is found
        return None, None, None, None, None
        
    book_val = book_row.iloc[0]["Book Value"]
    book_date = book_row.iloc[0]["Date"] if not book_row.empty else None

    # Calculate exit value and add to cash flows
    exit_val = book_val * exit_moic
    baseline_cf_orig = flows + [(book_date, book_val)]
    modeled_cf_orig = flows + [(exit_date, exit_val)]

    # Calculate management fees using individual company method with dynamic capital calls
    # Assume this is analyzing as first exit for company-level analysis
    is_first_exit = True
    
    fee_details = calculate_company_management_fees_with_hurdle(
        fee_date, 
        exit_date, 
        fee_balance, 
        company,
        hurdle_rate=0.08,
        include_capital_calls=include_capital_calls,
        is_first_exit=is_first_exit
    )
    
    total_fees = fee_details['total_fees']
    priority_return = fee_details['priority_return']
    new_fees = fee_details['new_fees']
    company_quarterly_fee = fee_details['company_quarterly_fee']
    company_proportion = fee_details['company_proportion']
    company_initial_balance = fee_details['initial_balance']
    capital_call_fees = fee_details.get('capital_call_fees', 0)
    capital_call_pr = fee_details.get('capital_call_pr', 0)
    mgmt_fees_only = fee_details.get('mgmt_fees_only', total_fees)
    
    # Calculate IRRs
    baseline_cf_agg = aggregate_cashflows_by_date(baseline_cf_orig) 
    baseline_irr = xirr(baseline_cf_agg)
    
    calculated_new_baseline_net_irr = None
    if baseline_irr is not None:
        calculated_new_baseline_net_irr = (1 + baseline_irr) * 0.9680 - 1

    # Adjust modeled cash flows for fee payment at exit
    modeled_cf_with_fees_orig = modeled_cf_orig + [(exit_date, -total_fees)]
    
    # Aggregate cash flows by date for accurate IRR calculation
    modeled_cf_agg = aggregate_cashflows_by_date(modeled_cf_orig)
    modeled_cf_with_fees_agg = aggregate_cashflows_by_date(modeled_cf_with_fees_orig)

    # Compute IRRs
    modeled_irr = xirr(modeled_cf_agg)
    irr_with_fees = xirr(modeled_cf_with_fees_agg)

    # Enhanced Fee Calculation Details DataFrame with Dynamic Capital Call Method
    days_to_exit = (exit_date - fee_date).days
    
    if include_capital_calls:
        fee_breakdown_df = pd.DataFrame({
            "Component": [
                "Company Quarterly Fee", 
                "Company's Initial Balance Share", 
                "New Mgmt Fees Accrued", 
                "Total Management Fees (No PR)", 
                "Capital Call Fees (Company Share)",
                "Capital Call PR (8% Dynamic)",
                "Total Company Fees Due"
            ],
            "Amount": [
                company_quarterly_fee,
                company_initial_balance, 
                new_fees, 
                mgmt_fees_only,
                capital_call_fees,
                capital_call_pr,
                total_fees
            ],
            "Calculation": [
                f"From Excel: ${company_quarterly_fee:,.2f} quarterly",
                f"({company_proportion:.4%} × ${fee_balance:,.0f})",
                f"{days_to_exit} days × (${company_quarterly_fee:,.0f} ÷ 91.25 days/qtr)",
                "Initial Share + New Fees (No PR)",
                f"Static principal × {company_proportion:.4%}",
                f"Dynamic calculation: 8% from call dates to {exit_date.strftime('%Y-%m-%d')}",
                "Mgmt Fees + Capital Calls + Dynamic Capital Call PR"
            ]
        })
    else:
        fee_breakdown_df = pd.DataFrame({
            "Component": [
                "Company Quarterly Fee", 
                "Company's Initial Balance Share", 
                "New Fees Accrued", 
                "Total Company Fees Due"
            ],
            "Amount": [
                company_quarterly_fee,
                company_initial_balance, 
                new_fees, 
                total_fees
            ],
            "Calculation": [
                f"From Excel: ${company_quarterly_fee:,.2f} quarterly",
                f"({company_proportion:.4%} × ${fee_balance:,.0f})",
                f"{days_to_exit} days × (${company_quarterly_fee:,.0f} ÷ 91.25 days/qtr)",
                "All Management Fees (No Priority Return)"
            ]
        })

    # MOIC Metrics
    net_investment = 0
    for _, row_data in cdata.iterrows():
        if row_data["Contributions"] < 0: 
            net_investment += -row_data["Contributions"] 
        elif row_data["Contributions"] > 0: 
            net_investment -= row_data["Contributions"] 
    
    net_investment = max(0, net_investment)
    
    baseline_moic_val = book_val / net_investment if net_investment > 0 else None
    modeled_moic_val = exit_val / net_investment if net_investment > 0 else None
    delta_moic_val = modeled_moic_val - baseline_moic_val if baseline_moic_val and modeled_moic_val else None

    # IRR Sensitivity with dynamic capital calls
    moics_range = [x / 10 for x in range(5, 101, 5)]
    irr_series = []
    fund_irr_series = [] # Placeholder for fund IRR

    for m_iter in moics_range: 
        val = book_val * m_iter
        test_cf_orig = flows + [(exit_date, val)]
        
        # Use individual company fee calculation with dynamic capital calls
        test_fee_details = calculate_company_management_fees_with_hurdle(
            fee_date, exit_date, fee_balance, company, 
            hurdle_rate=0.08, 
            include_capital_calls=include_capital_calls,
            is_first_exit=is_first_exit
        )
        test_total_fees = test_fee_details['total_fees']
        
        test_cf_with_fees_orig = test_cf_orig + [(exit_date, -test_total_fees)]
        test_cf_with_fees_agg = aggregate_cashflows_by_date(test_cf_with_fees_orig)
        
        irr_val = xirr(test_cf_with_fees_agg)
        irr_series.append(irr_val * 100 if irr_val else None)
        fund_irr_series.append(None)  # Placeholder

    irr_sensitivity_df = pd.DataFrame({
        "Exit MOIC": moics_range,
        "Company IRR (%)": irr_series,
        "Fund IRR (%)": fund_irr_series
    })

    # IRR Over Time with dynamic capital calls
    current_date = datetime.today()
    if exit_date.tzinfo is not None and current_date.tzinfo is None:
        current_date = pd.Timestamp(current_date).tz_localize(exit_date.tzinfo)
    elif exit_date.tzinfo is None and current_date.tzinfo is not None:
        exit_date = pd.Timestamp(exit_date).tz_localize(current_date.tzinfo)

    if current_date <= exit_date:
        date_range_pd = pd.date_range(start=current_date, end=exit_date, freq="QS")
    else:
        date_range_pd = pd.to_datetime([])

    time_series = {}
    fee_time_series = {}
    
    for dt_iter in date_range_pd: 
        tmp_fee_details = calculate_company_management_fees_with_hurdle(
            fee_date, dt_iter, fee_balance, company, 
            hurdle_rate=0.08,
            include_capital_calls=include_capital_calls,
            is_first_exit=is_first_exit
        )
        tmp_total_fees = tmp_fee_details['total_fees']
        
        ts_cf_orig = flows + [(dt_iter, exit_val - tmp_total_fees)]
        ts_cf_agg = aggregate_cashflows_by_date(ts_cf_orig)
        
        irr_val_ts = xirr(ts_cf_agg)
        if irr_val_ts:
            time_series[dt_iter.date()] = irr_val_ts * 100
            fee_time_series[dt_iter.date()] = tmp_total_fees

    irr_over_time_df = pd.DataFrame({
        "Exit Date": list(time_series.keys()),
        "IRR (%)": list(time_series.values()),
        "Total Fees ($)": list(fee_time_series.values())
    }) if time_series else pd.DataFrame(columns=["Exit Date", "IRR (%)", "Total Fees ($)"])

    # Priority Return Impact Analysis DataFrame with Dynamic Calculation
    priority_impact_data = []
    if time_series and include_capital_calls:
        for dt in sorted(list(time_series.keys())): 
            dt_timestamp = pd.Timestamp(dt)
            fee_details_impact = calculate_company_management_fees_with_hurdle(
                fee_date, dt_timestamp, fee_balance, company, 
                hurdle_rate=0.08,
                include_capital_calls=include_capital_calls,
                is_first_exit=is_first_exit
            )
            
            priority_impact_data.append({
                "Exit Date": dt,
                "Days from Balance": (dt_timestamp - fee_date).days,
                "Company Quarterly Fee": fee_details_impact['company_quarterly_fee'],
                "Management Fees": fee_details_impact['mgmt_fees_only'],
                "Capital Call Fees": fee_details_impact['capital_call_fees'],
                "Capital Call PR (Dynamic)": fee_details_impact['capital_call_pr'],
                "Total Fees": fee_details_impact['total_fees'],
                "PR as % of Total": (fee_details_impact['capital_call_pr'] / fee_details_impact['total_fees'] * 100) if fee_details_impact['total_fees'] > 0 else 0
            })
        
    impact_df = pd.DataFrame(priority_impact_data)

    # Summary metrics
    summary_metrics = {
        "Company Name": company,
        "Exit Date": exit_date.strftime('%Y-%m-%d'),
        "Book Value": book_val,
        "Book Date": book_date,
        "Modeled Exit Value": exit_val,
        "Baseline IRR": baseline_irr,
        "Baseline Net IRR": calculated_new_baseline_net_irr,
        "Company Quarterly Fee": company_quarterly_fee,
        "Company Proportion": company_proportion,
        "Company Initial Balance": company_initial_balance,
        "New Mgmt Fees to Exit": new_fees,
        "Management Fees (No PR)": mgmt_fees_only,
        "Capital Call Fees": capital_call_fees,
        "Capital Call PR (Dynamic)": capital_call_pr,
        "Total Priority Return": priority_return,
        "Total Fees Due": total_fees,
        "Modeled IRR (No Fees)": modeled_irr,
        "Modeled IRR (With Fees)": irr_with_fees,
        "Net Investment": net_investment,
        "Baseline MOIC": baseline_moic_val,
        "Modeled MOIC": modeled_moic_val,
        "Delta MOIC": delta_moic_val,
        "Include Capital Calls": include_capital_calls,
        "Is First Exit": is_first_exit,
        "Capital Call Method": "Dynamic PR calculation" if include_capital_calls else "No capital calls"
    }

    return summary_metrics, fee_breakdown_df, impact_df, irr_sensitivity_df, irr_over_time_df

def get_fund_analysis_data(data_df, edited_df, fee_balance, fee_date, q_fee, target_irr_val, company_list, include_capital_calls=True, company_fees=None, fee_mapping=None, gp_commitment=0, call_dates=None, investment_pr_amounts=None):
    """
    Calculates and returns all data for the Fund IRR from Multi-Exit Plan tab.
    Updated with dynamic capital call fee support and call dates from Investment PR tab.
    Now uses Investment PR amounts if available instead of calculating from cash flows.
    """
    latest_date = data_df["Date"].max()
    default_exit_date = (pd.to_datetime(latest_date) + pd.DateOffset(months=12)).date()

    company_moics = {}
    company_has_flows = {}
    company_net_investments = {}
    written_off_companies = []
    
    company_book_values = {}
    for comp_iter in company_list:
        comp_data_iter = data_df[data_df["Deal Name"] == comp_iter]
        
        # Use Investment PR amounts if available, otherwise calculate from cash flows
        if investment_pr_amounts and comp_iter in investment_pr_amounts:
            net_investment_val = investment_pr_amounts[comp_iter]
        else:
            # Fallback to cash flow calculation
            net_investment_val = 0
            for _, row_data in comp_data_iter.iterrows():
                if row_data["Contributions"] < 0: 
                    net_investment_val += -row_data["Contributions"] 
                elif row_data["Contributions"] > 0: 
                    net_investment_val -= row_data["Contributions"] 
        
        net_investment_val = max(0, net_investment_val)
        
        company_net_investments[comp_iter] = net_investment_val
        has_contributions_flag = net_investment_val > 0
        company_has_flows[comp_iter] = has_contributions_flag
        
        book_row_iter = comp_data_iter[comp_data_iter["Book Value"] > 0].sort_values("Date", ascending=False)
        
        if not book_row_iter.empty:
            company_moics[comp_iter] = 2.0
            company_book_values[comp_iter] = book_row_iter.iloc[0]["Book Value"]
        else:
            company_moics[comp_iter] = 0.0
            if has_contributions_flag:
                written_off_companies.append(comp_iter)
    
    # Use the edited_df passed in
    if "Has Contributions" not in edited_df.columns:
        edited_df = pd.merge(
            edited_df, 
            pd.DataFrame({
                "Company": company_list, 
                "Has Contributions": [company_has_flows.get(c, False) for c in company_list]
            }), 
            on="Company", 
            how="left"
        )
    if "Written Off" not in edited_df.columns:
        edited_df = pd.merge(
            edited_df,
            pd.DataFrame({
                "Company": company_list,
                "Written Off": [c in written_off_companies for c in company_list]
            }),
            on="Company",
            how="left"
        )

    for comp_name_iter in written_off_companies:
        edited_df.loc[edited_df["Company"] == comp_name_iter, "MOIC"] = 0.0

    exit_timeline = edited_df.sort_values("Exit Date").reset_index(drop=True)
    
    # Exit Plan Summary by Year
    years_summary = exit_timeline.groupby(pd.to_datetime(exit_timeline['Exit Date'], errors='coerce').dt.year).agg({
        'Company': 'count', 
        'MOIC': 'mean'
    }).reset_index()
    years_summary.columns = ['Year', 'Number of Exits', 'Average MOIC']
    
    # Calculate historical flows
    historical_flows = []
    for _, row_data_hist in data_df.iterrows():
        if not pd.isna(row_data_hist["Date"]):
            historical_flows.append((row_data_hist["Date"], row_data_hist["Distributions"] + row_data_hist["Contributions"]))
    
    # Process exits with dynamic capital call fees (only at first exit)
    fund_flows = historical_flows.copy()
    fee_tracker = []
    remaining_fee_balance = fee_balance
    last_fee_date = fee_date
    
    # Group exits by date
    exit_dates = {}
    for _, row_data_exit in exit_timeline.iterrows():
        exit_date_val = pd.to_datetime(row_data_exit["Exit Date"], errors='coerce')
        if pd.notna(exit_date_val):
            if exit_date_val not in exit_dates:
                exit_dates[exit_date_val] = []
            exit_dates[exit_date_val].append(row_data_exit)
    
    first_exit_flag = True
    exit_number = 0
    for exit_date_proc, companies_exiting in sorted(exit_dates.items()):
        exit_number += 1
        is_first_exit = (exit_number == 1)
        exit_value_total = 0
        
        # Calculate fees with dynamic capital calls (only at first exit)
        fee_details = calculate_management_fees_with_hurdle(
            last_fee_date, 
            exit_date_proc, 
            remaining_fee_balance, 
            q_fee,
            hurdle_rate=0.08,
            include_capital_calls=include_capital_calls,
            is_first_exit=is_first_exit
        )
        
        fees_for_this_exit = fee_details['total_fees']
        capital_call_amount = fee_details.get('capital_call_amount', 0)
        capital_call_pr = fee_details.get('capital_call_pr', 0)
        
        # Handle written-off companies
        written_off_value = 0
        if first_exit_flag:
            current_exiting_companies_list = [exit_info_item["Company"] for exit_info_item in companies_exiting]
            unexited_written_off_list = [comp_item for comp_item in written_off_companies 
                                   if comp_item not in current_exiting_companies_list]
            
            for written_off_comp_item in unexited_written_off_list:
                written_off_value += company_net_investments.get(written_off_comp_item, 0)
                
                if written_off_value > 0:
                    fee_tracker.append({
                        "Company": f"{written_off_comp_item} (Written Off)",
                        "Exit Date": exit_date_proc,
                        "MOIC": 0.0,
                        "Exit Value": 0,
                        "Management Fees": 0,
                        "Capital Call Fees": 0,
                        "Capital Call PR": 0,
                        "Total Fees": 0,
                        "Priority Return": 0,
                        "Fee Basis": 0,
                        "Status": "Written Off (Investment Deducted)",
                        "Is First Exit": is_first_exit,
                        "Capital Call Method": "Dynamic PR" if include_capital_calls else "No capital calls"
                    })
            
            first_exit_flag = False
        
        # Process each company exiting on this date
        for idx, exit_info_item_proc in enumerate(companies_exiting):
            company_proc = exit_info_item_proc["Company"]
            moic_proc = exit_info_item_proc["MOIC"]
            
            comp_data_proc = data_df[data_df["Deal Name"] == company_proc]
            book_row_proc = comp_data_proc[comp_data_proc["Book Value"] > 0].sort_values("Date", ascending=False)
            
            net_investment_proc = company_net_investments.get(company_proc, 0)
            
            exit_val_proc = 0
            if not book_row_proc.empty:
                book_val_proc = book_row_proc.iloc[0]["Book Value"] 
                exit_val_proc = net_investment_proc * moic_proc
                exit_value_total += exit_val_proc
                
                fee_tracker.append({
                    "Company": company_proc,
                    "Exit Date": exit_date_proc,
                    "MOIC": moic_proc,
                    "Exit Value": exit_val_proc,
                    "Management Fees": fee_details.get('mgmt_fees_only', 0) if idx == 0 else 0,
                    "Capital Call Fees": capital_call_amount if idx == 0 else 0,
                    "Capital Call PR": capital_call_pr if idx == 0 else 0,
                    "Total Fees": fees_for_this_exit if idx == 0 else 0,  # Only first company pays
                    "Priority Return": fee_details['priority_return'] if idx == 0 else 0,
                    "Fee Basis": fee_details['fee_basis'] if idx == 0 else 0,
                    "New Fees": fee_details['new_fees'] if idx == 0 else 0,
                    "Days": fee_details['days'] if idx == 0 else 0,
                    "Status": "Active",
                    "Is First Exit": is_first_exit,
                    "Capital Call Method": "Dynamic PR" if include_capital_calls else "No capital calls"
                })
            elif company_proc in written_off_companies:
                exit_val_proc = net_investment_proc * moic_proc 
                exit_value_total += exit_val_proc 
                
                fee_tracker.append({
                    "Company": company_proc,
                    "Exit Date": exit_date_proc,
                    "MOIC": moic_proc,
                    "Exit Value": exit_val_proc,
                    "Management Fees": fee_details.get('mgmt_fees_only', 0) if idx == 0 else 0,
                    "Capital Call Fees": capital_call_amount if idx == 0 else 0,
                    "Capital Call PR": capital_call_pr if idx == 0 else 0,
                    "Total Fees": fees_for_this_exit if idx == 0 else 0,
                    "Priority Return": fee_details['priority_return'] if idx == 0 else 0,
                    "Fee Basis": fee_details['fee_basis'] if idx == 0 else 0,
                    "New Fees": fee_details['new_fees'] if idx == 0 else 0,
                    "Days": fee_details['days'] if idx == 0 else 0,
                    "Status": "Written Off (Included in Exit)",
                    "Is First Exit": is_first_exit,
                    "Capital Call Method": "Dynamic PR" if include_capital_calls else "No capital calls"
                })
        
        net_exit_value = exit_value_total - fees_for_this_exit - written_off_value
        fund_flows.append((exit_date_proc, net_exit_value))
        
        # Reset for next period
        remaining_fee_balance = 0 
        last_fee_date = exit_date_proc

    # Add remaining companies at net investment value (changed from book value)
    for company_rem in company_list:
        if not any(ft_item["Company"] == company_rem for ft_item in fee_tracker):
            net_investment_rem = company_net_investments.get(company_rem, 0)
            
            if net_investment_rem > 0:
                # Use the latest date from the data for the remaining company
                comp_data_rem = data_df[data_df["Deal Name"] == company_rem]
                if not comp_data_rem.empty:
                    latest_date_rem = comp_data_rem["Date"].max()
                    fund_flows.append((latest_date_rem, net_investment_rem))
                else:
                    # Fallback to a default date if no data found
                    fund_flows.append((data_df["Date"].max(), net_investment_rem))
    
    # Calculate fund IRR
    fund_flows_agg = aggregate_cashflows_by_date(fund_flows)
    fund_irr_val = xirr(fund_flows_agg)
    
    # Calculate GP carry
    fund_moic_val, total_investment_val, total_fund_value_val = calculate_fund_moic(data_df, fee_tracker, edited_df, gp_commitment, company_net_investments)
    carry_calculation = calculate_gp_carry(data_df, total_fund_value_val, exit_timeline=exit_timeline, gp_commitment=gp_commitment, company_net_investments=company_net_investments, call_dates=call_dates)
    
    # Calculate hurdle return breakdown by company
    hurdle_breakdown_df = calculate_hurdle_return_breakdown(data_df, exit_timeline=exit_timeline, hurdle_rate=0.08, gp_commitment=gp_commitment, call_dates=call_dates)

    # Enhanced Fee Accrual Timeline with Dynamic Capital Calls
    timeline_data = []
    sorted_exit_dates_list = sorted(exit_dates.keys())
    
    if sorted_exit_dates_list:
        current_fee_balance_tl = fee_balance
        current_fee_date_tl = fee_date
        first_exit_tl_flag = True
        exit_num_tl = 0
        
        current_timeline_q_fee = q_fee # Initialize with the full fund quarterly fee
        if company_fees is None: company_fees = {}
        if fee_mapping is None: fee_mapping = {}
        
        for i_tl, exit_date_tl in enumerate(sorted_exit_dates_list):
            exit_num_tl += 1
            is_first_exit_tl = (exit_num_tl == 1)
            
            # Get companies exiting in this period FIRST (before calculating fees)
            companies_exiting_tl = []
            for exit_info_tl_item in exit_dates[exit_date_tl]:
                company_tl = exit_info_tl_item["Company"]
                companies_exiting_tl.append(company_tl)
            
            # Calculate fees with dynamic capital calls
            fee_details_tl = calculate_management_fees_with_hurdle(
                current_fee_date_tl,
                exit_date_tl,
                current_fee_balance_tl,
                current_timeline_q_fee, # MODIFIED HERE
                hurdle_rate=0.08,
                include_capital_calls=include_capital_calls,
                is_first_exit=is_first_exit_tl
            )
            
            period_exit_value_tl = 0
            net_investments_deducted_tl = 0
            
            # Handle written-off value
            written_off_value_tl = 0
            if first_exit_tl_flag:
                current_exiting_companies_tl_list = [ei["Company"] for ei in exit_dates[exit_date_tl]]
                unexited_written_off_tl_list = [c_item for c_item in written_off_companies
                                       if c_item not in current_exiting_companies_tl_list]
                
                for written_off_comp_tl_item in unexited_written_off_tl_list:
                    written_off_value_tl += company_net_investments.get(written_off_comp_tl_item, 0)
                
                first_exit_tl_flag = False
            
            # Calculate exit values (companies_exiting_tl already populated above)
            for exit_info_tl_item in exit_dates[exit_date_tl]:
                company_tl = exit_info_tl_item["Company"]
                moic_tl = exit_info_tl_item["MOIC"]
                
                net_investment_tl = company_net_investments.get(company_tl, 0)
                net_investments_deducted_tl += net_investment_tl
                
                if net_investment_tl > 0:
                    period_exit_value_tl += net_investment_tl * moic_tl
            
            period_label_tl = "Initial Balance to First Exit" if i_tl == 0 else f"Exit {i_tl} to Exit {i_tl+1}"
            
            # Calculate cumulative IRR up to this exit
            cumulative_flows_tl = historical_flows.copy()
            
            # --- Modification for cumulative IRR calculation using potentially reduced fees ---
            # We need a separate q_fee tracker for this cumulative IRR calculation,
            # as it recalculates history for each timeline point.
            # The main `current_timeline_q_fee` is for the sequential timeline display.
            cumulative_q_fee_for_irr_calc = q_fee
            temp_company_fees_for_irr_calc = company_fees.copy() # Use a copy to avoid modifying original

            for j_tl in range(i_tl + 1):
                exit_j_tl = sorted_exit_dates_list[j_tl]
                exit_j_value_tl = 0
                # is_j_first = (j_tl == 0) # This was for capital calls, which are handled by fee_details_tl

                companies_exiting_in_j_period = [info["Company"] for info in exit_dates[exit_j_tl]]

                for exit_info_j_tl_item in exit_dates[exit_j_tl]:
                    company_j_tl = exit_info_j_tl_item["Company"]
                    moic_j_tl = exit_info_j_tl_item["MOIC"]
                    
                    net_investment_j_tl = company_net_investments.get(company_j_tl, 0)
                    
                    if net_investment_j_tl > 0:
                        exit_j_value_tl += net_investment_j_tl * moic_j_tl
                
                # Calculate fees for this exit with dynamic capital calls
                # For cumulative IRR, we use the q_fee relevant for *that specific period in history*
                q_fee_for_this_irr_period = cumulative_q_fee_for_irr_calc
                
                if j_tl == 0:
                    exit_j_fee_details = calculate_management_fees_with_hurdle(
                        fee_date, exit_j_tl, fee_balance, q_fee_for_this_irr_period,
                        hurdle_rate=0.08,
                        include_capital_calls=include_capital_calls, # Capital calls only on first overall exit
                        is_first_exit=True # This is the first exit in the *overall fund timeline*
                    )
                    exit_j_fees_tl = exit_j_fee_details['total_fees']
                    if written_off_value_tl > 0 and is_first_exit_tl: # Apply WO only at the very first exit of the fund
                         exit_j_value_tl -= written_off_value_tl
                else:
                    prev_exit_tl = sorted_exit_dates_list[j_tl-1]
                    exit_j_fee_details = calculate_management_fees_with_hurdle(
                        prev_exit_tl, exit_j_tl, 0, q_fee_for_this_irr_period,
                        hurdle_rate=0.08,
                        include_capital_calls=include_capital_calls, # Capital calls only on first overall exit
                        is_first_exit=False # Not the first exit in the *overall fund timeline*
                    )
                    exit_j_fees_tl = exit_j_fee_details['total_fees']
                
                cumulative_flows_tl.append((exit_j_tl, exit_j_value_tl - exit_j_fees_tl))

                # Reduce q_fee for the *next* iteration of this cumulative IRR calculation loop
                exiting_companies_q_fees_in_j_period = 0
                for company_deal_name_j in companies_exiting_in_j_period:
                    fee_name_key_j = fee_mapping.get(company_deal_name_j, company_deal_name_j) if fee_mapping else company_deal_name_j
                    exiting_companies_q_fees_in_j_period += temp_company_fees_for_irr_calc.get(fee_name_key_j, 0)
                cumulative_q_fee_for_irr_calc -= exiting_companies_q_fees_in_j_period
                cumulative_q_fee_for_irr_calc = max(0, cumulative_q_fee_for_irr_calc)
            # --- End of modification for cumulative IRR calculation ---
            
            cumulative_irr_tl = xirr(aggregate_cashflows_by_date(cumulative_flows_tl))
            
            timeline_data.append({
                "Period": period_label_tl,
                "Start Date": current_fee_date_tl,
                "End Date": exit_date_tl,
                "Days": fee_details_tl['days'],
                "Starting Balance": current_fee_balance_tl,
                "New Mgmt Fees": fee_details_tl['new_fees'], # This uses current_timeline_q_fee
                "Mgmt Fee Total": fee_details_tl.get('mgmt_fees_only', fee_details_tl.get('mgmt_total_fees', 0)),
                "Capital Call Fees": fee_details_tl.get('capital_call_amount', 0),
                "Capital Call PR (Dynamic)": fee_details_tl.get('capital_call_pr', 0),
                "Total Fees Due": fee_details_tl['total_fees'],
                "Net Investments": net_investments_deducted_tl,
                "Written Off Value": written_off_value_tl if i_tl == 0 else 0, # WO applied only at first fund exit
                "Companies Exiting": ", ".join(companies_exiting_tl),
                "Exit Value": period_exit_value_tl,
                "Fund IRR After Exit": cumulative_irr_tl, # This uses the revised cumulative IRR calc
                "Is First Exit": is_first_exit_tl,
                "Capital Call Method": "Dynamic PR" if include_capital_calls else "No capital calls"
            })
            
            # Reduce the quarterly fee for the next period's timeline calculation
            exiting_companies_specific_q_fees_for_period = 0
            for company_deal_name in companies_exiting_tl:
                # Use fee_mapping to get the name used in company_fees keys
                fee_name_key = fee_mapping.get(company_deal_name, company_deal_name) if fee_mapping else company_deal_name
                exiting_companies_specific_q_fees_for_period += company_fees.get(fee_name_key, 0) if company_fees else 0
            
            current_timeline_q_fee -= exiting_companies_specific_q_fees_for_period
            current_timeline_q_fee = max(0, current_timeline_q_fee)

            current_fee_date_tl = exit_date_tl
            current_fee_balance_tl = 0
    
    fee_timeline_df = pd.DataFrame(timeline_data) if timeline_data else pd.DataFrame()

    # Exit and Fee Details with Dynamic Capital Calls
    fee_df = pd.DataFrame(fee_tracker) if fee_tracker else pd.DataFrame()
    
    if not fee_df.empty:
        fee_df["Net Investment"] = fee_df["Company"].apply(
            lambda x_val: company_net_investments.get(x_val.replace(" (Written Off)", ""), 0)
        )
        display_columns = ["Company", "Net Investment", "MOIC", "Exit Date", "Exit Value", 
                          "Management Fees", "Capital Call Fees", "Capital Call PR", 
                          "Total Fees", "Status", "Is First Exit", "Capital Call Method"]
        fee_df_display = fee_df[[col for col in display_columns if col in fee_df.columns]]
    else:
        fee_df_display = pd.DataFrame()

    # Calculate totals with dynamic amounts
    total_mgmt_fees = sum(td["Mgmt Fee Total"] for td in timeline_data) if timeline_data else 0
    total_capital_calls = sum(td["Capital Call Fees"] for td in timeline_data) if timeline_data else 0
    total_capital_pr = sum(td["Capital Call PR (Dynamic)"] for td in timeline_data) if timeline_data else 0
    total_all_fees = sum(td["Total Fees Due"] for td in timeline_data) if timeline_data else 0

    # Create summary metrics with dynamic capital call information
    summary_metrics = {
        "Fund IRR (with 8% hurdle)": fund_irr_val,
        "Fund MOIC": fund_moic_val,
        "Total Fees Paid (incl. Priority Return)": total_all_fees,
        "Total Profit": total_fund_value_val - total_investment_val,
        "Hurdle Return (8%)": total_capital_pr,  # Dynamic amount
        "Excess Profit": total_fund_value_val - total_investment_val - total_capital_pr,
        "GP Carry (20%)": carry_calculation['gp_carry'],
        "Initial Balance": fee_balance,
        "New Fees Accrued (Fund-wide)": sum(td["New Mgmt Fees"] for td in timeline_data) if timeline_data else 0,
        "Priority Return (8%) (Fund-wide)": total_capital_pr,  # Dynamic amount
        "Total Fees Paid (Fund-wide)": total_all_fees,
        "Total Fund Value": total_fund_value_val,
        "Capital Call Method": "Dynamic PR calculation" if include_capital_calls else "No capital calls",
        "Capital Call Principal (Static)": 288277746 if include_capital_calls else 0,
        "Capital Call PR (Dynamic)": total_capital_pr if include_capital_calls else 0
    }

    # Fund IRR Progression Over Time - no changes needed for this section
    irr_timeline_data_df = pd.DataFrame(fund_flows_agg, columns=["Date", "Cash Flow"])
    irr_timeline_list = []
    if not irr_timeline_data_df.empty:
        irr_timeline_data_df["Date_Str"] = irr_timeline_data_df["Date"].dt.strftime('%Y-%m-%d')
        irr_timeline_data_df = irr_timeline_data_df.sort_values("Date")
        for i_prog in range(1, len(irr_timeline_data_df) + 1):
            subset_flows_prog = [(irr_timeline_data_df["Date"].iloc[j_prog], irr_timeline_data_df["Cash Flow"].iloc[j_prog]) for j_prog in range(i_prog)]
            irr_val_prog = xirr(subset_flows_prog)
            if irr_val_prog is not None:
                irr_timeline_list.append({
                    "Date": irr_timeline_data_df["Date"].iloc[i_prog-1],
                    "Date_Str": irr_timeline_data_df["Date_Str"].iloc[i_prog-1],
                    "IRR": irr_val_prog * 100
                })
    irr_df_prog = pd.DataFrame(irr_timeline_list) if irr_timeline_list else pd.DataFrame(columns=["Date", "Date_Str", "IRR"])

    # IRR Sensitivity to MOIC with Dynamic Capital Calls
    moic_range_sens = [0.5, 0.75, 1.0, 1.5, 2.0, 2.5, 3.0, 4.0, 5.0]
    sensitivity_results_list = []
    
    for test_moic_sens in moic_range_sens:
        test_timeline_sens = exit_timeline.copy()
        test_timeline_sens["MOIC"] = test_moic_sens
        
        test_flows_sens = historical_flows.copy()
        
        test_exit_dates_sens = {}
        for _, row_sens_data in test_timeline_sens.iterrows():
            exit_date_sens = pd.to_datetime(row_sens_data["Exit Date"], errors='coerce')
            if pd.notna(exit_date_sens):
                if exit_date_sens not in test_exit_dates_sens:
                    test_exit_dates_sens[exit_date_sens] = []
                test_exit_dates_sens[exit_date_sens].append(row_sens_data)
        
        test_fee_date_sens = fee_date
        test_fee_balance_sens = fee_balance
        test_exit_num = 0
        test_current_quarterly_fee_sens = q_fee  # Initialize current quarterly fee for sensitivity
        
        for exit_date_sens_proc, companies_exiting_sens in sorted(test_exit_dates_sens.items()):
            test_exit_num += 1
            test_is_first = (test_exit_num == 1)
            exit_value_total_sens = 0
            
            # Calculate fees with dynamic capital calls
            test_fee_details_sens = calculate_management_fees_with_hurdle(
                test_fee_date_sens, exit_date_sens_proc, test_fee_balance_sens, q_fee, 
                hurdle_rate=0.08,
                include_capital_calls=include_capital_calls,
                is_first_exit=test_is_first
            )
            test_period_fees_sens = test_fee_details_sens['total_fees']
            
            for _, exit_info_sens_item in enumerate(companies_exiting_sens):
                company_sens = exit_info_sens_item["Company"]
                moic_sens = exit_info_sens_item["MOIC"]
                
                net_investment_sens = company_net_investments.get(company_sens, 0)
                
                if net_investment_sens > 0:
                    exit_val_sens = net_investment_sens * moic_sens
                    exit_value_total_sens += exit_val_sens
            
            test_flows_sens.append((exit_date_sens_proc, exit_value_total_sens - test_period_fees_sens))
            
            # Subtract quarterly fees of exiting companies for the next sensitivity period
            exiting_companies_sens_q_fees = 0
            for comp in companies_exiting_sens:
                # Use fee mapping to get the correct fee company name
                fee_company_name = fee_mapping.get(comp['Company'], comp['Company']) if fee_mapping else comp['Company']
                company_fee = company_fees.get(fee_company_name, 0) if company_fees else 0
                exiting_companies_sens_q_fees += company_fee
            
            test_current_quarterly_fee_sens -= exiting_companies_sens_q_fees
            test_current_quarterly_fee_sens = max(0, test_current_quarterly_fee_sens) # Ensure fee doesn't go below zero
            
            test_fee_date_sens = exit_date_sens_proc
            test_fee_balance_sens = 0
        
        test_flows_agg_sens = aggregate_cashflows_by_date(test_flows_sens)
        test_irr_sens = xirr(test_flows_agg_sens)
        
        sensitivity_results_list.append({
            "MOIC": test_moic_sens,
            "IRR": test_irr_sens * 100 if test_irr_sens else None,
            "DPI": test_moic_sens
        })
    
    sens_df = pd.DataFrame(sensitivity_results_list) if sensitivity_results_list else pd.DataFrame(columns=["MOIC", "IRR", "DPI"])

    return summary_metrics, edited_df, years_summary, fee_timeline_df, fee_df_display, irr_df_prog, sens_df, hurdle_breakdown_df


def export_ipo_scenario_to_excel(ipo_schedules, writer, sheet_name="IPO Distribution Schedule"):
    """
    Export IPO scenario distribution schedule to Excel.
    
    Args:
        ipo_schedules: List of IPO distribution schedules
        writer: ExcelWriter object
        sheet_name: Name of the sheet to create
    """
    if not ipo_schedules:
        return
    
    # Create distribution DataFrame
    dist_df = create_ipo_distribution_df(ipo_schedules)
    
    # Create summary DataFrame
    summary_data = []
    for schedule in ipo_schedules:
        summary_data.append({
            'Company': schedule['company_name'],
            'Exit Value': schedule['exit_value'],
            'Distribution Pattern': schedule['pattern'].title(),
            'Number of Distributions': schedule['num_distributions'],
            'Distribution Period': '3 Years',
            'First Distribution': schedule['distribution_dates'][0].strftime('%Y-%m-%d'),
            'Last Distribution': schedule['distribution_dates'][-1].strftime('%Y-%m-%d')
        })
    
    summary_df = pd.DataFrame(summary_data)
    
    # Write summary to Excel
    summary_df.to_excel(writer, sheet_name=sheet_name, index=False, startrow=0)
    
    # Add a blank row
    blank_row = summary_df.shape[0] + 2
    
    # Write distribution details
    dist_df.to_excel(writer, sheet_name=sheet_name, index=False, startrow=blank_row)
    
    # Format the worksheet
    worksheet = writer.sheets[sheet_name]
    
    # Set column widths
    worksheet.set_column('A:A', 20)  # Company
    worksheet.set_column('B:B', 15)  # Distribution #
    worksheet.set_column('C:C', 12)  # Date
    worksheet.set_column('D:D', 15)  # Amount
    worksheet.set_column('E:E', 12)  # Percentage
    worksheet.set_column('F:F', 15)  # Exit Value
    
    # Add number formatting
    money_format = writer.book.add_format({'num_format': '$#,##0'})
    percent_format = writer.book.add_format({'num_format': '0.0%'})
    
    # Apply formatting to summary section
    for row in range(1, summary_df.shape[0] + 1):
        worksheet.write(row, 1, summary_df.iloc[row-1]['Exit Value'], money_format)
    
    # Apply formatting to distribution section
    start_row = blank_row + 1
    for row in range(start_row, start_row + dist_df.shape[0]):
        col_idx = row - start_row
        if col_idx < len(dist_df):
            worksheet.write(row, 3, dist_df.iloc[col_idx]['Amount'], money_format)
            worksheet.write(row, 4, dist_df.iloc[col_idx]['Percentage'] / 100, percent_format)
            worksheet.write(row, 5, dist_df.iloc[col_idx]['Exit Value'], money_format)
