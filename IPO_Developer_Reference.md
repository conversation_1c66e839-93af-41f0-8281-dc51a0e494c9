# IPO Scenario - Developer Quick Reference

## Module Structure

### Core Modules
- `ipo_scenario.py` - Main module with backward compatibility
- `ipo_scenario_advanced.py` - Phase 2 calculation engine
- `ipo_scenario_phase2.py` - Enhanced UI components

### Key Functions

#### Calculate Distributions
```python
from ipo_scenario_advanced import calculate_ipo_distributions_advanced

schedule = calculate_ipo_distributions_advanced(
    company_name="ABC Corp",
    exit_value=*********,
    exit_date=datetime(2026, 12, 31),
    distribution_years=3,
    distribution_pattern='custom',  # 'equal', 'front', 'back', 'custom'
    custom_percentages=[25, 20, 15, 15, 15, 10],
    distribution_frequency='quarterly',  # 'quarterly', 'semi-annual', 'annual'
    fee_allocation='pro-rata'  # 'first', 'pro-rata', 'realized'
)
```

#### Apply Fees
```python
from ipo_scenario_advanced import apply_fees_to_ipo_distributions

schedule_with_fees = apply_fees_to_ipo_distributions(
    schedule,
    total_fees=5000000,
    fee_allocation='pro-rata'
)
```

#### Display IPO Section
```python
from ipo_scenario import display_ipo_scenario_section

ipo_schedules = display_ipo_scenario_section(
    edited_df,
    company_net_investments,
    fee_tracker,  # Optional
    total_fees    # Optional
)
```

## Session State Variables

```python
# IPO selection state
st.session_state.ipo_selected_companies  # Set of selected company names
st.session_state.ipo_scenario_changed     # Flag for recalculation
st.session_state.ipo_distribution_pattern # Selected pattern
st.session_state.ipo_distribution_frequency # Selected frequency
st.session_state.ipo_distribution_years   # Distribution period

# Custom percentages (if pattern is 'custom')
st.session_state.ipo_scenario_custom_pcts # List of percentages
```

## Data Structures

### IPO Schedule Dictionary
```python
{
    'company_name': str,
    'exit_value': float,
    'num_distributions': int,
    'distribution_dates': List[datetime],
    'distribution_amounts': List[float],
    'distribution_percentages': List[float],
    'pattern': str,
    'frequency': str,
    'fee_allocation': str,
    'custom_percentages': List[float] or None,
    
    # After fee application:
    'distribution_amounts_after_fees': List[float],
    'fee_amounts': List[float],
    'total_fees': float
}
```

## Integration Points

### Fund Analysis Tab
```python
# In tab_fund_analysis.py
from ipo_scenario import display_ipo_scenario_section, apply_ipo_to_cashflows

# Display IPO section
ipo_schedules = display_ipo_scenario_section(edited_df, company_net_investments)

# Apply to cash flows
if ipo_schedules:
    fund_flows = apply_ipo_to_cashflows(fund_flows, ipo_schedules, fee_tracker, company_net_investments)
```

### Excel Export
```python
# In excel_export_helpers.py
from excel_export_helpers import export_ipo_scenario_to_excel

if ipo_schedules:
    export_ipo_scenario_to_excel(ipo_schedules, writer, "IPO Distributions")
```

## Validation Functions

```python
from ipo_scenario_advanced import validate_custom_percentages

is_valid, error_msg = validate_custom_percentages([25, 25, 25, 25])
# Returns: (True, None) if valid
# Returns: (False, "Error message") if invalid
```

## Quick Examples

### 1. Equal Distribution
```python
schedule = calculate_ipo_distributions_advanced(
    "Company A", *********, datetime(2026, 12, 31),
    distribution_pattern='equal'
)
```

### 2. Custom Quarterly
```python
schedule = calculate_ipo_distributions_advanced(
    "Company B", *********, datetime(2027, 6, 30),
    distribution_years=2,
    distribution_pattern='custom',
    custom_percentages=[30, 25, 25, 20],
    distribution_frequency='quarterly'
)
```

### 3. With Fees
```python
# First distribution fee allocation
schedule_with_fees = apply_fees_to_ipo_distributions(
    schedule, 10000000, 'first'
)

# Access net amounts
net_distributions = schedule_with_fees['distribution_amounts_after_fees']
```

## Common Patterns

### Check if IPO Active
```python
ipo_active = st.session_state.get('enable_ipo_scenario', False)
selected_companies = st.session_state.get('ipo_selected_companies', set())
```

### Get Distribution Summary
```python
from ipo_scenario_phase2 import create_ipo_distribution_df_advanced

dist_df = create_ipo_distribution_df_advanced(ipo_schedules)
# Returns DataFrame with columns:
# Company, Distribution #, Date, Gross Amount, Fee Amount, Net Amount, Percentage
```

## Debugging Tips

1. **Validation Issues**: Check `validate_custom_percentages()` output
2. **Fee Allocation**: Verify total fees don't exceed distributions
3. **Session State**: Use `st.write(st.session_state)` to debug
4. **Cash Flows**: Log before/after applying IPO distributions

## Testing

```bash
# Run basic tests
python test_ipo_scenario.py

# Run Phase 2 tests
python test_ipo_phase2.py
```

---
For full documentation, see:
- `IPO_Scenario_Implementation_Plan.md`
- `IPO_Phase2_Documentation.md`