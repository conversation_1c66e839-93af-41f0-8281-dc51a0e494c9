#!/usr/bin/env python3
"""
Enhanced template_manager_sqlite.py with fixes for common template saving issues

Key improvements:
1. Better error handling and logging
2. More robust cache management
3. Improved data serialization
4. Enhanced debugging information
5. Fixed caching issues that might prevent proper template saving/loading
"""

import streamlit as st
import sqlite3
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
import pandas as pd
import logging
import traceback

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SQLiteTemplateManager:
    """Enhanced SQLite-based template manager with improved error handling and caching"""
    
    def __init__(self, db_path: str = "templates.db"):
        self.db_path = db_path
        self.init_database()
        self._debug_mode = False  # Disable debug logging
    
    def init_database(self):
        """Initialize SQLite database with enhanced error handling"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Enable foreign key constraints
                conn.execute("PRAGMA foreign_keys = ON")
                
                # Create templates table with additional metadata
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS templates (
                        name TEXT PRIMARY KEY,
                        data TEXT NOT NULL,
                        created_date TEXT NOT NULL,
                        modified_date TEXT NOT NULL,
                        version INTEGER DEFAULT 1,
                        category TEXT DEFAULT 'General',
                        tags TEXT DEFAULT '',
                        size_bytes INTEGER DEFAULT 0,
                        checksum TEXT DEFAULT '',
                        status TEXT DEFAULT 'active'
                    )
                """)
                
                # Create template history table
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS template_history (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        template_name TEXT NOT NULL,
                        action TEXT NOT NULL,
                        timestamp TEXT NOT NULL,
                        user_session TEXT,
                        details TEXT DEFAULT '',
                        success BOOLEAN DEFAULT 1
                    )
                """)
                
                # Create index for faster queries
                conn.execute("""
                    CREATE INDEX IF NOT EXISTS idx_templates_modified 
                    ON templates(modified_date DESC)
                """)
                
                conn.commit()
                logger.info(f"Database initialized successfully at {self.db_path}")
                
        except Exception as e:
            error_msg = f"Database initialization failed: {e}"
            logger.error(error_msg)
            if hasattr(st, 'error'):
                st.error(error_msg)
            raise
    
    def save_template(self, template_name: str, parameters: Dict, company_scenarios: List[Dict], 
                     category: str = "General", tags: List[str] = None) -> bool:
        """Enhanced template saving with better error handling and cache management"""
        try:
            if self._debug_mode:
                logger.info(f"Attempting to save template: {template_name}")
                logger.info(f"Parameters: {parameters}")
                logger.info(f"Company scenarios: {len(company_scenarios)} items")
            
            # Validate inputs
            if not template_name or not template_name.strip():
                error_msg = "Template name cannot be empty"
                logger.error(error_msg)
                if hasattr(st, 'error'):
                    st.error(error_msg)
                return False
            
            template_name = template_name.strip()
            
            # Prepare template data with enhanced structure
            template_data = {
                "template_name": template_name,
                "parameters": self._serialize_data(parameters),
                "company_scenarios": self._serialize_data(company_scenarios),
                "version": 1,
                "save_timestamp": datetime.now().isoformat(),
                "metadata": {
                    "parameter_count": len(parameters) if parameters else 0,
                    "scenario_count": len(company_scenarios) if company_scenarios else 0,
                    "category": category,
                    "tags": tags or []
                }
            }
            
            # Serialize for storage
            data_json = json.dumps(template_data, default=str, indent=2)
            data_size = len(data_json.encode('utf-8'))
            
            # Calculate checksum for data integrity
            import hashlib
            checksum = hashlib.md5(data_json.encode('utf-8')).hexdigest()
            
            now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            tags_str = ",".join(tags or [])
            
            # Save to database with transaction
            with sqlite3.connect(self.db_path) as conn:
                # Check if template already exists
                cursor = conn.execute("SELECT name FROM templates WHERE name = ?", (template_name,))
                exists = cursor.fetchone() is not None
                
                # Insert or update template
                conn.execute("""
                    INSERT OR REPLACE INTO templates 
                    (name, data, created_date, modified_date, version, category, tags, size_bytes, checksum, status)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (template_name, data_json, now, now, 1, category, tags_str, data_size, checksum, 'active'))
                
                # Log the action with details
                action = "UPDATE" if exists else "CREATE"
                details = f"Size: {data_size} bytes, Scenarios: {len(company_scenarios)}, Parameters: {len(parameters)}"
                
                conn.execute("""
                    INSERT INTO template_history (template_name, action, timestamp, user_session, details, success)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (template_name, action, now, self._get_session_id(), details, True))
                
                conn.commit()
                logger.info(f"Template '{template_name}' saved successfully")
            
            # Force clear all Streamlit caches to ensure fresh data
            self._clear_all_caches()
            
            if self._debug_mode:
                logger.info(f"Template saved and caches cleared for: {template_name}")
            
            return True
            
        except Exception as e:
            error_msg = f"Error saving template '{template_name}': {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            
            # Log failed attempt
            try:
                with sqlite3.connect(self.db_path) as conn:
                    conn.execute("""
                        INSERT INTO template_history (template_name, action, timestamp, user_session, details, success)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (template_name, "SAVE_FAILED", datetime.now().strftime("%Y-%m-%d %H:%M:%S"), 
                          self._get_session_id(), str(e), False))
                    conn.commit()
            except:
                pass  # Don't fail on logging failure
            
            if hasattr(st, 'error'):
                st.error(error_msg)
            return False
    
    def load_template(self, template_name: str) -> Optional[Dict]:
        """Enhanced template loading with better error handling"""
        try:
            if self._debug_mode:
                logger.info(f"Loading template: {template_name}")
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT data, created_date, modified_date, version, category, checksum 
                    FROM templates 
                    WHERE name = ? AND status = 'active'
                """, (template_name,))
                row = cursor.fetchone()
                
                if row:
                    data, created, modified, version, category, checksum = row
                    
                    # Verify data integrity
                    import hashlib
                    calculated_checksum = hashlib.md5(data.encode('utf-8')).hexdigest()
                    if checksum and checksum != calculated_checksum:
                        logger.warning(f"Checksum mismatch for template '{template_name}' - data may be corrupted")
                    
                    # Parse template data
                    template_data = json.loads(data)
                    template_data.update({
                        'created_date': created,
                        'modified_date': modified,
                        'version': version,
                        'category': category,
                        'checksum': checksum
                    })
                    
                    # Deserialize data
                    template_data['parameters'] = self._deserialize_data(template_data['parameters'])
                    template_data['company_scenarios'] = self._deserialize_data(template_data['company_scenarios'])
                    
                    # Log successful load
                    self._log_action(template_name, "LOAD", f"Successfully loaded template", True)
                    
                    if self._debug_mode:
                        logger.info(f"Template '{template_name}' loaded successfully")
                    
                    return template_data
                else:
                    logger.warning(f"Template '{template_name}' not found or inactive")
                    return None
                    
        except Exception as e:
            error_msg = f"Error loading template '{template_name}': {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            
            # Log failed attempt
            self._log_action(template_name, "LOAD_FAILED", str(e), False)
            
            if hasattr(st, 'error'):
                st.error(error_msg)
            return None
    
    def list_templates(self) -> List[str]:
        """Enhanced template listing with better error handling"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT name FROM templates 
                    WHERE status = 'active'
                    ORDER BY modified_date DESC
                """)
                templates = [row[0] for row in cursor.fetchall()]
                
                if self._debug_mode:
                    logger.info(f"Listed {len(templates)} templates")
                
                return templates
        except Exception as e:
            error_msg = f"Error listing templates: {str(e)}"
            logger.error(error_msg)
            if hasattr(st, 'error'):
                st.error(error_msg)
            return []
    
    def delete_template(self, template_name: str) -> bool:
        """Enhanced template deletion with soft delete option"""
        try:
            if self._debug_mode:
                logger.info(f"Deleting template: {template_name}")
            
            with sqlite3.connect(self.db_path) as conn:
                # Soft delete by marking as inactive
                cursor = conn.execute("""
                    UPDATE templates 
                    SET status = 'deleted', modified_date = ?
                    WHERE name = ?
                """, (datetime.now().strftime("%Y-%m-%d %H:%M:%S"), template_name))
                
                if cursor.rowcount > 0:
                    self._log_action(template_name, "DELETE", f"Template marked as deleted", True)
                    conn.commit()
                    
                    # Clear caches
                    self._clear_all_caches()
                    
                    logger.info(f"Template '{template_name}' deleted successfully")
                    return True
                else:
                    logger.warning(f"Template '{template_name}' not found for deletion")
                    return False
                    
        except Exception as e:
            error_msg = f"Error deleting template '{template_name}': {str(e)}"
            logger.error(error_msg)
            self._log_action(template_name, "DELETE_FAILED", str(e), False)
            if hasattr(st, 'error'):
                st.error(error_msg)
            return False
    
    def get_template_details(self, template_name: str) -> Optional[Dict]:
        """Enhanced template details retrieval"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT created_date, modified_date, version, category, tags, size_bytes, checksum
                    FROM templates WHERE name = ? AND status = 'active'
                """, (template_name,))
                row = cursor.fetchone()
                
                if row:
                    created, modified, version, category, tags, size_bytes, checksum = row
                    
                    # Get company count and target IRR from stored data
                    cursor = conn.execute("SELECT data FROM templates WHERE name = ?", (template_name,))
                    data_row = cursor.fetchone()
                    num_companies = 0
                    target_irr = None
                    
                    if data_row:
                        try:
                            template_data = json.loads(data_row[0])
                            num_companies = len(template_data.get('company_scenarios', []))
                            target_irr = template_data.get('parameters', {}).get('target_irr')
                        except:
                            pass
                    
                    return {
                        "name": template_name,
                        "created": created,
                        "modified": modified,
                        "version": version,
                        "category": category,
                        "tags": tags.split(',') if tags else [],
                        "size_kb": round(size_bytes / 1024, 1),
                        "num_companies": num_companies,
                        "target_irr": target_irr,
                        "checksum": checksum
                    }
                return None
        except Exception as e:
            logger.error(f"Error getting template details: {str(e)}")
            return None
    
    def get_template_statistics(self) -> Dict:
        """Enhanced template statistics retrieval"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Basic counts
                cursor = conn.execute("SELECT COUNT(*), SUM(size_bytes) FROM templates WHERE status = 'active'")
                count, total_size = cursor.fetchone()
                
                # Categories breakdown
                cursor = conn.execute("""
                    SELECT category, COUNT(*) FROM templates 
                    WHERE status = 'active' 
                    GROUP BY category
                """)
                categories = dict(cursor.fetchall())
                
                # Recent activity
                cursor = conn.execute("""
                    SELECT action, COUNT(*) FROM template_history 
                    WHERE timestamp > datetime('now', '-7 days')
                    GROUP BY action
                """)
                recent_activity = dict(cursor.fetchall())
                
                return {
                    'total_templates': count or 0,
                    'total_size_mb': round((total_size or 0) / (1024 * 1024), 2),
                    'categories': categories or {},
                    'recent_activity': recent_activity or {}
                }
        except Exception as e:
            logger.error(f"Error getting template statistics: {str(e)}")
            return {
                'total_templates': 0,
                'total_size_mb': 0,
                'categories': {},
                'recent_activity': {}
            }
    
    def export_template(self, template_name: str) -> Optional[str]:
        """Export template as JSON string"""
        template_data = self.load_template(template_name)
        if template_data:
            return json.dumps(template_data, indent=4, default=str)
        return None
    
    def import_template(self, json_data: str, overwrite: bool = False) -> Optional[str]:
        """Import template from JSON string"""
        try:
            template_data = json.loads(json_data)
            template_name = template_data.get("template_name")
            
            if not template_name:
                if hasattr(st, 'error'):
                    st.error("Template must have a name")
                return None
            
            if not overwrite and template_name in self.list_templates():
                if hasattr(st, 'error'):
                    st.error(f"Template '{template_name}' already exists")
                return None
            
            parameters = template_data.get("parameters", {})
            company_scenarios = template_data.get("company_scenarios", [])
            category = template_data.get("category", "Imported")
            
            if self.save_template(template_name, parameters, company_scenarios, category):
                return template_name
            return None
            
        except Exception as e:
            error_msg = f"Error importing template: {str(e)}"
            logger.error(error_msg)
            if hasattr(st, 'error'):
                st.error(error_msg)
            return None
    
    def _serialize_data(self, obj: Any) -> Any:
        """Enhanced data serialization with better type handling"""
        if isinstance(obj, dict):
            return {k: self._serialize_data(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._serialize_data(item) for item in obj]
        elif isinstance(obj, (pd.Timestamp, datetime)):
            return obj.isoformat()
        elif hasattr(obj, 'date') and callable(obj.date):
            return obj.date().isoformat()
        elif hasattr(obj, 'isoformat'):
            return obj.isoformat()
        elif pd.isna(obj):
            return None
        elif isinstance(obj, (int, float, str, bool)) or obj is None:
            return obj
        else:
            # Handle other types by converting to string
            return str(obj)
    
    def _deserialize_data(self, obj: Any) -> Any:
        """Enhanced data deserialization with better type handling"""
        if isinstance(obj, dict):
            return {k: self._deserialize_data(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._deserialize_data(item) for item in obj]
        elif isinstance(obj, str) and self._is_date_string(obj):
            try:
                return pd.to_datetime(obj).date()
            except:
                return obj
        else:
            return obj
    
    def _is_date_string(self, s: str) -> bool:
        """Enhanced date string detection"""
        try:
            if len(s) >= 8 and ('-' in s or '/' in s or 'T' in s):
                pd.to_datetime(s)
                return True
        except:
            pass
        return False
    
    def _get_session_id(self) -> str:
        """Enhanced session ID generation"""
        try:
            if hasattr(st, 'session_state'):
                if 'session_id' not in st.session_state:
                    import uuid
                    st.session_state.session_id = str(uuid.uuid4())[:8]
                return st.session_state.session_id
            else:
                import uuid
                return str(uuid.uuid4())[:8]
        except:
            return "unknown"
    
    def _log_action(self, template_name: str, action: str, details: str = "", success: bool = True):
        """Enhanced action logging with detailed information"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO template_history (template_name, action, timestamp, user_session, details, success)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (template_name, action, datetime.now().strftime("%Y-%m-%d %H:%M:%S"), 
                      self._get_session_id(), details, success))
                conn.commit()
        except Exception as e:
            logger.warning(f"Failed to log action: {e}")
    
    def _clear_all_caches(self):
        """Enhanced cache clearing for better data consistency"""
        try:
            if hasattr(st, 'cache_data'):
                st.cache_data.clear()
            if hasattr(st, 'cache_resource'):
                st.cache_resource.clear()
            if hasattr(st, 'session_state'):
                # Clear specific cache-related session state items
                cache_keys = [k for k in st.session_state.keys() if 'cache' in k.lower() or 'template' in k.lower()]
                for key in cache_keys:
                    if key in st.session_state:
                        del st.session_state[key]
            
            logger.info("All caches cleared successfully")
        except Exception as e:
            logger.warning(f"Error clearing caches: {e}")
    
    def get_debug_info(self) -> Dict:
        """Get debugging information about the template system"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Basic stats
                cursor = conn.execute("SELECT COUNT(*) FROM templates WHERE status = 'active'")
                active_count = cursor.fetchone()[0]
                
                cursor = conn.execute("SELECT COUNT(*) FROM templates WHERE status = 'deleted'")
                deleted_count = cursor.fetchone()[0]
                
                cursor = conn.execute("SELECT COUNT(*) FROM template_history")
                history_count = cursor.fetchone()[0]
                
                # Recent activity
                cursor = conn.execute("""
                    SELECT action, success, COUNT(*) 
                    FROM template_history 
                    WHERE timestamp > datetime('now', '-1 day')
                    GROUP BY action, success
                    ORDER BY COUNT(*) DESC
                """)
                recent_activity = cursor.fetchall()
                
                return {
                    "active_templates": active_count,
                    "deleted_templates": deleted_count,
                    "history_entries": history_count,
                    "recent_activity": recent_activity,
                    "database_path": self.db_path,
                    "debug_mode": self._debug_mode
                }
        except Exception as e:
            logger.error(f"Error getting debug info: {e}")
            return {"error": str(e)}

# Create enhanced global instance
sqlite_template_manager = SQLiteTemplateManager()
