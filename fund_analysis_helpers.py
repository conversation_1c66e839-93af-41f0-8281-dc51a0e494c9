import streamlit as st
import pandas as pd
import datetime
from financial_calculations import calculate_management_fees_with_hurdle

def prepare_company_data(data_df, company_list, investment_pr_amounts):
    """
    Prepares initial company data for analysis.
    """
    company_moics = {}
    company_has_flows = {}
    company_net_investments = {}
    written_off_companies = []
    company_book_values = {}
    
    for comp_iter in company_list:
        comp_data_iter = data_df[data_df["Deal Name"] == comp_iter]
        
        if investment_pr_amounts and comp_iter in investment_pr_amounts:
            net_investment_val = investment_pr_amounts[comp_iter]
        else:
            net_investment_val = 0
            for _, row_data in comp_data_iter.iterrows():
                if row_data["Contributions"] < 0: 
                    net_investment_val += -row_data["Contributions"] 
                elif row_data["Contributions"] > 0: 
                    net_investment_val -= row_data["Contributions"] 
        
        net_investment_val = max(0, net_investment_val)
        
        company_net_investments[comp_iter] = net_investment_val
        has_contributions_flag = net_investment_val > 0
        company_has_flows[comp_iter] = has_contributions_flag
        
        book_row_iter = comp_data_iter[comp_data_iter["Book Value"] > 0].sort_values("Date", ascending=False)
        
        if not book_row_iter.empty:
            company_moics[comp_iter] = 2.0
            company_book_values[comp_iter] = book_row_iter.iloc[0]["Book Value"]
        else:
            company_moics[comp_iter] = 0.0
            if has_contributions_flag:
                written_off_companies.append(comp_iter)

    return company_net_investments, company_book_values, company_moics, company_has_flows, written_off_companies


def create_plan_df(company_list, company_net_investments, company_book_values, company_moics, company_has_flows, written_off_companies, default_exit_date):
    """
    Creates the initial DataFrame for the exit plan.
    """
    if 'template_company_scenarios' in st.session_state:
        template_scenarios = st.session_state.template_company_scenarios
        scenario_map = {s['company']: s for s in template_scenarios}
        
        for company in company_list:
            if company in scenario_map:
                scenario = scenario_map[company]
                if company not in written_off_companies:
                    company_moics[company] = scenario['moic']
        
        plan_df = pd.DataFrame({
            "Company": company_list,
            "Net Investment ($)": [company_net_investments.get(c, 0) for c in company_list],
            "Book Value ($)": [company_book_values.get(c, 0) for c in company_list],
            "MOIC": [company_moics.get(c, 2.0) for c in company_list],
            "Exit Date": [scenario_map.get(c, {}).get('exit_date', default_exit_date) if c not in written_off_companies else default_exit_date for c in company_list],
            "Has Contributions": [company_has_flows.get(c, False) for c in company_list],
            "Written Off": [c in written_off_companies for c in company_list]
        })
        
        # Don't delete template_company_scenarios immediately - let it persist for reuse
        # del st.session_state.template_company_scenarios
        st.success("✅ Applied company scenarios from template")
    else:
        plan_df = pd.DataFrame({
            "Company": company_list,
            "Net Investment ($)": [company_net_investments.get(c, 0) for c in company_list],
            "Book Value ($)": [company_book_values.get(c, 0) for c in company_list],
            "MOIC": [company_moics.get(c, 2.0) for c in company_list],
            "Exit Date": [default_exit_date] * len(company_list),
            "Has Contributions": [company_has_flows.get(c, False) for c in company_list],
            "Written Off": [c in written_off_companies for c in company_list]
        })
    return plan_df


def initialize_session_state_for_plan_editor(plan_df):
    """
    Initialize session state with plan data if not already set.
    Also handles template loading by updating existing session state.
    """
    # Check if template was just loaded or if we need to reinitialize
    template_loaded = 'template_company_scenarios' in st.session_state
    force_reinit = template_loaded or 'plan_editor_data' not in st.session_state
    
    if force_reinit:
        if 'plan_editor_data' not in st.session_state:
            st.session_state.plan_editor_data = {}
        
        for idx, row in plan_df.iterrows():
            company_name = str(row["Company"])
            company_key = company_name.replace(' ', '_').replace('.', '_')
            
            # Update MOIC value (overwrite if template loaded)
            st.session_state.plan_editor_data[f"moic_{company_key}"] = float(row.get("MOIC", 2.0))
            
            # Handle exit date
            if pd.notna(row.get("Exit Date")):
                try:
                    exit_date = pd.to_datetime(row["Exit Date"]).date()
                except:
                    exit_date = datetime.date(2025, 12, 31)
            else:
                exit_date = datetime.date(2025, 12, 31)
            
            # Update exit date value (overwrite if template loaded)
            st.session_state.plan_editor_data[f"exit_date_{company_key}"] = exit_date
        
        # Clean up template scenarios after use
        if template_loaded and 'template_company_scenarios' in st.session_state:
            del st.session_state.template_company_scenarios


def display_and_process_plan_editor(plan_df, written_off_companies):
    """
    Enhanced plan editor with session state management to prevent freezing.
    """
    st.write("### Exit Plan Editor")
    
    # Initialize session state
    initialize_session_state_for_plan_editor(plan_df)
    
    # Add "Calculate" button to control when expensive operations run
    col1, col2, col3 = st.columns([1, 1, 2])
    with col1:
        calculate_button = st.button("🔄 Calculate Results", type="primary")
    with col2:
        auto_calculate = st.checkbox("Auto-calculate", value=False, help="Automatically calculate on input changes (may cause freezing)")
    
    if auto_calculate:
        st.warning("⚠️ Auto-calculate is enabled. This may cause the app to freeze with large datasets.")
    
    # Create header row with LP Invested Capital and Exit Value columns
    header_cols = st.columns([3, 2, 2, 2, 2])
    with header_cols[0]:
        st.write("**Company**")
    with header_cols[1]:
        st.write("**LP Invested Capital ($M)**")
    with header_cols[2]:
        st.write("**MOIC**")
    with header_cols[3]:
        st.write("**Exit Date**")
    with header_cols[4]:
        st.write("**Exit Value ($M)**")
    
    # Track if any changes were made
    changes_made = False
    
    # Process each company - only update session state, don't build data structures yet
    for idx, row in plan_df.iterrows():
        cols = st.columns([3, 2, 2, 2, 2])
        
        company_name = str(row["Company"])
        company_key = company_name.replace(' ', '_').replace('.', '_')
        
        with cols[0]:
            st.write(company_name)
        
        with cols[1]:
            # Display LP Invested Capital
            net_investment = row.get("Net Investment ($)", 0)
            
            # Display LP Invested Capital in millions with proper formatting
            if net_investment >= 1000000:
                gross_invested_display = f"${net_investment / 1000000:.1f}M"
            else:
                gross_invested_display = f"${net_investment / 1000:.0f}K"
            
            st.markdown(f"**{gross_invested_display}**")
        
        with cols[2]:
            # Create unique keys
            moic_key = f"moic_{company_key}"
            
            # Get current value from session state
            current_moic = st.session_state.plan_editor_data.get(moic_key, float(row.get("MOIC", 2.0)))
            
            # For written off companies, force MOIC to 0
            if company_name in written_off_companies:
                moic_val = st.number_input(
                    f"MOIC for {company_name}",
                    value=0.0,
                    min_value=0.0,
                    max_value=0.0,
                    format="%.2f",
                    key=f"input_{moic_key}",
                    label_visibility="collapsed",
                    disabled=True,
                    help="Written off - MOIC fixed at 0.0"
                )
                st.session_state.plan_editor_data[moic_key] = 0.0
            else:
                moic_val = st.number_input(
                    f"MOIC for {company_name}",
                    value=current_moic,
                    min_value=0.0,
                    max_value=100.0,
                    step=0.1,
                    format="%.2f",
                    key=f"input_{moic_key}",
                    label_visibility="collapsed"
                )
                
                # Always update session state with current widget value
                st.session_state.plan_editor_data[moic_key] = moic_val
                
                # Check if value changed for UI feedback
                if abs(moic_val - current_moic) > 0.001:
                    changes_made = True
        
        with cols[3]:
            # Create unique key for date
            date_key = f"exit_date_{company_key}"
            
            # Get current value from session state
            current_date = st.session_state.plan_editor_data.get(date_key)
            if current_date is None:
                current_date = datetime.date(2025, 12, 31)
            
            # Convert date to string for text input
            current_date_str = current_date.strftime("%Y-%m-%d")
            
            date_str = st.text_input(
                f"Exit Date for {company_name}",
                value=current_date_str,
                key=f"input_{date_key}",
                label_visibility="collapsed",
                help="Enter date in YYYY-MM-DD format"
            )
            
            # Parse the date string and validate
            try:
                date_val = datetime.datetime.strptime(date_str, "%Y-%m-%d").date()
                # Always update session state with current widget value
                st.session_state.plan_editor_data[date_key] = date_val
                
                # Check if date changed for UI feedback
                if date_val != current_date:
                    changes_made = True
            except ValueError:
                # If parsing fails, keep the current date and show warning
                st.session_state.plan_editor_data[date_key] = current_date
                if date_str != current_date_str:  # Only show warning if user actually changed the text
                    st.warning(f"Invalid date format for {company_name}. Please use YYYY-MM-DD format.")
        
        with cols[4]:
            # Calculate and display Exit Value (MOIC × LP Invested Capital)
            current_moic_for_calc = st.session_state.plan_editor_data.get(moic_key, float(row.get("MOIC", 2.0)))
            exit_value = net_investment * current_moic_for_calc
            
            # Display Exit Value in millions with proper formatting
            if exit_value >= 1000000:
                exit_value_display = f"${exit_value / 1000000:.1f}M"
            else:
                exit_value_display = f"${exit_value / 1000:.0f}K"
            
            st.markdown(f"**{exit_value_display}**")
    
    # Show status indicators
    if changes_made and not auto_calculate:
        st.info("📝 Changes detected. Click 'Calculate Results' to update analysis.")
    
    # Only build the dataframe when explicitly requested or on first load
    should_recalculate = calculate_button or (auto_calculate and changes_made) or 'fund_analysis_edited_df' not in st.session_state
    
    if should_recalculate:
        # Build edited data using session state values
        edited_data = []
        for idx, row in plan_df.iterrows():
            company_name = str(row["Company"])
            company_key = company_name.replace(' ', '_').replace('.', '_')
            moic_key = f"moic_{company_key}"
            date_key = f"exit_date_{company_key}"
            
            net_investment = row.get("Net Investment ($)", 0)
            moic_value = st.session_state.plan_editor_data.get(moic_key, 2.0)
            exit_value = net_investment * moic_value
            
            edited_data.append({
                "Company": company_name,
                "MOIC": moic_value,
                "Exit Date": st.session_state.plan_editor_data.get(date_key),
                "Net Investment ($)": net_investment,
                "Book Value ($)": row.get("Book Value ($)", 0),
                "Has Contributions": row.get("Has Contributions", False),
                "Written Off": company_name in written_off_companies,
                "Exit Value": exit_value
            })
        
        # Create the edited dataframe
        edited_df = pd.DataFrame(edited_data)
        
        # Ensure data types are correct
        edited_df["MOIC"] = pd.to_numeric(edited_df["MOIC"], errors='coerce').fillna(0.0)
        edited_df["Exit Date"] = pd.to_datetime(edited_df["Exit Date"])
        edited_df["Exit Value"] = pd.to_numeric(edited_df["Exit Value"], errors='coerce').fillna(0.0)
        
        # Re-apply written off status
        for comp_name_iter in written_off_companies:
            edited_df.loc[edited_df["Company"] == comp_name_iter, "MOIC"] = 0.0
            edited_df.loc[edited_df["Company"] == comp_name_iter, "Exit Value"] = 0.0
        
        # Update session state
        st.session_state.fund_analysis_edited_df = edited_df
        st.session_state.should_recalculate = True
        
        if calculate_button:
            st.success("✅ Calculations updated!")
    else:
        # Use existing dataframe from session state
        edited_df = st.session_state.get('fund_analysis_edited_df')
        if edited_df is None:
            # Fallback: create minimal dataframe if somehow missing
            edited_df = plan_df.copy()
        st.session_state.should_recalculate = False
    
    return edited_df


def process_exits_and_fees(data_df, exit_timeline, fee_balance, fee_date, q_fee, written_off_companies, company_net_investments, include_capital_calls, fee_mapping, company_fees, company_book_values, ipo_schedules=None):
    """
    Processes exits and calculates fees over time.
    """
    historical_flows = []
    for _, row_data_hist in data_df.iterrows():
        if not pd.isna(row_data_hist["Date"]):
            historical_flows.append((row_data_hist["Date"], row_data_hist["Distributions"] + row_data_hist["Contributions"]))

    fund_flows = historical_flows.copy()
    fee_tracker = []
    remaining_fee_balance = fee_balance
    last_fee_date = fee_date
    
    # Add written off company investments to fund flows
    for written_off_comp in written_off_companies:
        comp_data = data_df[data_df["Deal Name"] == written_off_comp]
        for _, row in comp_data.iterrows():
            if row["Contributions"] < 0:
                fund_flows.append((row["Date"], row["Contributions"]))
    
    exit_dates = {}
    for _, row_data_exit in exit_timeline.iterrows():
        if pd.isna(row_data_exit["Exit Date"]):
            continue
        exit_date_val = pd.to_datetime(row_data_exit["Exit Date"])
        if exit_date_val not in exit_dates:
            exit_dates[exit_date_val] = []
        exit_dates[exit_date_val].append(row_data_exit)
    
    first_exit_flag = True
    exit_number = 0
    current_quarterly_fee = q_fee
    
    for exit_date_proc, companies_exiting in sorted(exit_dates.items()):
        exit_number += 1
        is_first_exit = (exit_number == 1)
        exit_value_total = 0

        # Check if this is an IPO exit
        is_ipo_exit = False
        if ipo_schedules:
            for schedule in ipo_schedules:
                if schedule['company_name'] in [c['Company'] for c in companies_exiting]:
                    is_ipo_exit = True
                    break
        
        if is_ipo_exit:
            fees_for_this_exit = 0
            fee_details = {'total_fees': 0, 'priority_return': 0, 'fee_basis': 0, 'new_fees': 0, 'days': 0, 'capital_call_amount': 0, 'capital_call_pr': 0, 'mgmt_fees_only': 0}
        else:
            fee_details = calculate_management_fees_with_hurdle(
                last_fee_date, exit_date_proc, remaining_fee_balance, current_quarterly_fee,
                hurdle_rate=0.08, include_capital_calls=include_capital_calls, is_first_exit=is_first_exit
            )
            fees_for_this_exit = fee_details['total_fees']
        
        # Handle written off companies at first exit
        written_off_value = 0
        if first_exit_flag:
            current_exiting_companies_list = [exit_info_item["Company"] for exit_info_item in companies_exiting]
            unexited_written_off_list = [comp_item for comp_item in written_off_companies 
                                   if comp_item not in current_exiting_companies_list]
            
            for written_off_comp_item in unexited_written_off_list:
                written_off_value += company_net_investments.get(written_off_comp_item, 0)
                
                if written_off_value > 0:
                    fee_tracker.append({
                        "Company": f"{written_off_comp_item} (Written Off)",
                        "Exit Date": exit_date_proc, "MOIC": 0.0, "Exit Value": 0, "Management Fees": 0,
                        "Capital Call Fees": 0, "Capital Call PR": 0, "Total Fees": 0, "Priority Return": 0,
                        "Fee Basis": 0, "Status": "Written Off (Investment Deducted)", "Is First Exit": is_first_exit,
                        "Net Investment": company_net_investments.get(written_off_comp_item, 0),
                        "Book Value": company_book_values.get(written_off_comp_item, 0)
                    })
            
            first_exit_flag = False
        
        # Process exiting companies
        for idx, exit_info_item_proc in enumerate(companies_exiting):
            company_proc = exit_info_item_proc["Company"]
            moic_proc = exit_info_item_proc["MOIC"]
            
            comp_data_proc = data_df[data_df["Deal Name"] == company_proc]
            book_row_proc = comp_data_proc[comp_data_proc["Book Value"] > 0].sort_values("Date", ascending=False)
            
            net_investment_proc = company_net_investments.get(company_proc, 0)
            
            # Define fee variables before if/else block so they're available in both branches
            capital_call_amount = fee_details.get('capital_call_amount', 0) if idx == 0 else 0
            capital_call_pr = fee_details.get('capital_call_pr', 0) if idx == 0 else 0
            mgmt_fees_only = fee_details.get('mgmt_fees_only', 0) if idx == 0 else 0
            
            exit_val_proc = 0
            if not book_row_proc.empty:
                book_val_proc = book_row_proc.iloc[0]["Book Value"] 
                exit_val_proc = net_investment_proc * moic_proc
                exit_value_total += exit_val_proc
                
                fee_tracker.append({
                    "Company": company_proc, "Exit Date": exit_date_proc, "MOIC": moic_proc, "Exit Value": exit_val_proc,
                    "Management Fees": mgmt_fees_only, "Capital Call Fees": capital_call_amount, "Capital Call PR": capital_call_pr,
                    "Total Fees": fees_for_this_exit if idx == 0 else 0, "Priority Return": fee_details['priority_return'] if idx == 0 else 0,
                    "Fee Basis": fee_details['fee_basis'] if idx == 0 else 0, "New Fees": fee_details['new_fees'] if idx == 0 else 0,
                    "Days": fee_details['days'] if idx == 0 else 0, "Status": "Active", "Is First Exit": is_first_exit,
                    "Net Investment": net_investment_proc, "Book Value": book_val_proc
                })
            else:
                # Company with no book value
                exit_val_proc = net_investment_proc * moic_proc
                exit_value_total += exit_val_proc
                
                fee_tracker.append({
                    "Company": company_proc, "Exit Date": exit_date_proc, "MOIC": moic_proc, "Exit Value": exit_val_proc,
                    "Management Fees": mgmt_fees_only if idx == 0 else 0, "Capital Call Fees": capital_call_amount if idx == 0 else 0, 
                    "Capital Call PR": capital_call_pr if idx == 0 else 0,
                    "Total Fees": fees_for_this_exit if idx == 0 else 0, "Priority Return": fee_details['priority_return'] if idx == 0 else 0,
                    "Fee Basis": fee_details['fee_basis'] if idx == 0 else 0, "New Fees": fee_details['new_fees'] if idx == 0 else 0,
                    "Days": fee_details['days'] if idx == 0 else 0, "Status": "Active", "Is First Exit": is_first_exit,
                    "Net Investment": net_investment_proc,
                    "Book Value": company_book_values.get(company_proc, 0)
                })
        
        # Add exit to fund flows
        net_exit_value = exit_value_total - fees_for_this_exit - written_off_value
        fund_flows.append((exit_date_proc, net_exit_value))
        
        # Reduce quarterly fee for exiting companies
        fees_to_remove = 0
        for exit_info in companies_exiting:
            company_name = exit_info["Company"]
            # Look up the fee for the exiting company
            if company_fees and company_name in company_fees:
                company_fee = company_fees.get(company_name, 0)
                fees_to_remove += company_fee
            elif company_fees and fee_mapping and company_name in fee_mapping:
                # Try to use fee mapping if direct lookup fails
                mapped_name = fee_mapping.get(company_name)
                if mapped_name in company_fees:
                    company_fee = company_fees.get(mapped_name, 0)
                    fees_to_remove += company_fee
        
        # Reset for next period
        remaining_fee_balance = 0
        last_fee_date = exit_date_proc
        # CRITICAL CHANGE: Reduce the quarterly fee instead of resetting to original
        current_quarterly_fee = max(0, current_quarterly_fee - fees_to_remove)
        
        # Add debug information to track fee reduction
        if fees_to_remove > 0:
            print(f"DEBUG: Reduced quarterly fee by ${fees_to_remove:,.2f} after exit on {exit_date_proc}")
            print(f"DEBUG: New quarterly fee is ${current_quarterly_fee:,.2f}")
    
    return fund_flows, fee_tracker, exit_dates
