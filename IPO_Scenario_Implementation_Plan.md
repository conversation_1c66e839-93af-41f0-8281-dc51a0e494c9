# IPO Scenario Implementation Plan

## 1. Feature Overview
- Add IPO scenario option to the Fund Analysis tab
- Allow users to select which companies will go through IPO
- Generate bi-yearly distribution schedules over 3 years for selected companies
- Update fund-level IRR and MOIC calculations based on the distribution schedule

## 2. UI/UX Design

### Location in Fund Analysis Tab:
- Add a new section after the "Exit Plan Editor" 
- Title: "IPO Scenario Analysis"

### Components:
1. **IPO Selection Interface**
   - Checkbox column in the existing exit plan table OR
   - Separate section with company list and checkboxes
   - Visual indicator (icon/badge) for IPO-selected companies

2. **Distribution Parameters**
   - Global settings for all IPO companies:
     - Distribution period (default: 3 years)
     - Distribution frequency (default: bi-yearly/semi-annual)
     - Distribution pattern options:
       - Equal distributions
       - Front-loaded (higher early distributions)
       - Back-loaded (higher later distributions)
       - Custom percentage allocation

3. **Distribution Preview Table**
   - Show distribution schedule for each IPO-selected company
   - Columns: Company, Distribution Date, Amount, Percentage of Exit Value
   - Summary row showing totals

## 3. Data Model Changes

### New Data Structures Needed:
1. **IPO Configuration**
   ```
   - company_name
   - is_ipo_scenario (boolean)
   - distribution_pattern (equal/front/back/custom)
   - distribution_percentages (array of percentages for each period)
   ```

2. **Distribution Schedule**
   ```
   - company_name
   - distribution_dates (array of dates)
   - distribution_amounts (array of amounts)
   - distribution_percentages (array of percentages)
   ```

## 4. Calculation Logic

### Distribution Calculation:
1. **For each IPO-selected company:**
   - Take the exit value from the existing exit plan
   - Apply distribution pattern to calculate amounts
   - Generate bi-yearly dates starting from exit date
   - Account for management fees and carry on each distribution

2. **Fee Treatment Options:**
   - Option A: Deduct all fees at first distribution
   - Option B: Pro-rata fees across all distributions
   - Option C: Fees only on realized distributions

3. **IRR Impact:**
   - Replace single exit cash flow with multiple distribution cash flows
   - Recalculate fund IRR with staggered distributions
   - Show comparison between immediate exit vs IPO scenario

## 5. Integration Points

### Modify Existing Functions:
1. **fund_analysis_helpers.py**
   - Extend `create_plan_df` to include IPO scenario column
   - Add IPO distribution logic to `process_exits_and_fees`

2. **financial_calculations.py**
   - Create new function for IPO distribution calculations
   - Modify fee calculations to handle staggered distributions

3. **fund_analysis_waterfall.py**
   - Update waterfall visualization to show distribution timeline
   - Add option to display cumulative vs individual distributions

## 6. Session State Management

### New Session State Variables:
- `ipo_selected_companies`: Set of company names selected for IPO
- `ipo_distribution_pattern`: Global or per-company distribution pattern
- `ipo_distribution_schedule`: Calculated distribution schedules
- `ipo_scenario_enabled`: Boolean to toggle IPO scenario mode

## 7. Visualization Requirements

1. **Timeline View**
   - Gantt-style chart showing distribution periods
   - Color coding for different companies
   - Milestone markers for distribution dates

2. **Cash Flow Impact Chart**
   - Compare immediate exit vs IPO scenario cash flows
   - Show cumulative distributions over time

3. **Enhanced Metrics Display**
   - Side-by-side comparison of metrics:
     - Standard Exit IRR vs IPO Scenario IRR
     - Time-weighted returns
     - DPI (Distributions to Paid-In) progression

## 8. Export Functionality

### Excel Export Additions:
1. New worksheet: "IPO Distribution Schedule"
2. Detailed distribution breakdown by company
3. Quarterly/annual summary of expected distributions

### PDF Report Enhancements:
1. IPO scenario summary section
2. Distribution timeline visualization
3. Comparative analysis table

## 9. Implementation Phases

**Phase 1: Basic IPO Selection** ✅ COMPLETED
- Add checkboxes to select companies
- Simple equal distribution calculation
- Basic distribution table display

**Phase 2: Advanced Distribution Patterns** ✅ COMPLETED
- Multiple distribution pattern options
- Custom percentage allocation
- Fee allocation options
- Distribution frequency selection (quarterly/semi-annual/annual)
- Variable distribution periods (1-5 years)
- Visualization of distribution timeline

**Phase 3: Visualization & Analysis** ✅ COMPLETED
- Distribution timeline (Gantt chart)
- Cash flow comparison charts
- DPI progression tracking
- Time-weighted returns analysis
- Scenario comparison metrics
- Advanced analytics dashboard

**Phase 4: Export & Reporting** 🚧 PENDING
- Excel export with distribution details
- PDF report enhancements
- Distribution calendar export

## 10. Technical Considerations

1. **Performance**
   - Cache IPO calculations separately
   - Only recalculate when IPO selections change
   - Optimize for large number of companies

2. **Validation**
   - Ensure exit dates are future dates for IPO scenarios
   - Validate distribution percentages sum to 100%
   - Check for conflicts with written-off companies

3. **User Experience**
   - Clear visual feedback when IPO scenario is active
   - Easy toggle between standard and IPO scenarios
   - Preserve IPO selections when switching views

## 11. Sample IPO Distribution Table Structure

| Company | Exit Value | Distribution 1 | Distribution 2 | Distribution 3 | Distribution 4 | Distribution 5 | Distribution 6 |
|---------|------------|----------------|----------------|----------------|----------------|----------------|----------------|
| Company A | $100M | $16.67M (6mo) | $16.67M (12mo) | $16.67M (18mo) | $16.67M (24mo) | $16.67M (30mo) | $16.67M (36mo) |
| Company B | $200M | $33.33M (6mo) | $33.33M (12mo) | $33.33M (18mo) | $33.33M (24mo) | $33.33M (30mo) | $33.33M (36mo) |

## 12. Key Benefits

1. **More Realistic Modeling**: IPO scenarios better reflect real-world exit dynamics
2. **Risk Management**: Shows impact of delayed liquidity on returns
3. **LP Communication**: Better tools for explaining distribution timelines
4. **Scenario Planning**: Ability to compare multiple exit strategies

This implementation plan provides a structured approach to adding IPO scenario functionality while maintaining compatibility with the existing IRR model system.