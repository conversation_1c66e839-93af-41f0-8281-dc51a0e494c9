import streamlit as st
import pandas as pd
import traceback # Added for enhanced error logging
import plotly.graph_objects as go

from financial_calculations import (
    xirr,
    aggregate_cashflows_by_date,
    calculate_fund_moic,
    calculate_management_fees_with_hurdle,
)

def display_capital_recovery_summary(recovery_metrics, written_off_companies):
    """Display capital recovery summary above the waterfall chart."""
    
    if written_off_companies:
        st.info(f"""
        **Capital Recovery Status:**
        - Total Investment: ${recovery_metrics['total_capital_invested']:,.0f}
        - Written-off Capital: ${recovery_metrics['written_off_capital']:,.0f} ({', '.join(written_off_companies)})
        - Capital Recovery: {recovery_metrics['recovery_percentage']:.1f}%
        - Written-off Coverage: {recovery_metrics['written_off_recovery_percentage']:.1f}%
        """)

def add_capital_recovery_milestones(fig, waterfall_data_wf, total_investment_wf, written_off_capital, y_axis_max):
    """Add visual milestone markers for capital recovery events."""
    
    for i, item in enumerate(waterfall_data_wf):
        if "Capital_Recovered" in item:
            capital_recovered = item["Capital_Recovered"]
            
            # Mark when full capital is recovered
            if capital_recovered >= total_investment_wf and "capital_recovery_marked" not in item:
                fig.add_shape(
                    type="line",
                    x0=i-0.4, y0=total_investment_wf, x1=i+0.4, y1=total_investment_wf,
                    line=dict(color="green", width=3, dash="dash"),
                )
                fig.add_annotation(
                    x=i, y=total_investment_wf,
                    text="💰 Full Capital Recovered",
                    showarrow=False,
                    yshift=15,
                    font=dict(color="green", size=11, family="Arial"),
                    bgcolor="rgba(255,255,255,0.9)",
                    bordercolor="green",
                    borderwidth=1
                )
                item["capital_recovery_marked"] = True
            
            # Mark when written-off capital is covered
            if "Written_Off_Coverage" in item and written_off_capital > 0:
                written_off_coverage = item["Written_Off_Coverage"]
                if written_off_coverage >= written_off_capital and "writeoff_recovery_marked" not in item:
                    fig.add_shape(
                        type="line",
                        x0=i-0.4, y0=written_off_capital, x1=i+0.4, y1=written_off_capital,
                        line=dict(color="blue", width=2, dash="dot"),
                    )
                    fig.add_annotation(
                        x=i, y=written_off_capital,
                        text="🔄 Written-off Capital Covered",
                        showarrow=False,
                        yshift=-25,
                        font=dict(color="blue", size=10, family="Arial"),
                        bgcolor="rgba(255,255,255,0.9)",
                        bordercolor="blue",
                        borderwidth=1
                    )
                    item["writeoff_recovery_marked"] = True

def display_waterfall_chart(fee_tracker, company_net_investments, data_df, total_value, carry_calculation, include_capital_calls, gp_commitment, edited_df, ipo_schedules=None):
    """
    Enhanced waterfall chart that provides different views for IPO scenarios.
    
    Args:
        fee_tracker: List of exit information
        company_net_investments: Dictionary of company investments
        data_df: DataFrame with company data
        total_value: Total fund value
        carry_calculation: GP carry calculation results
        include_capital_calls: Whether to include capital call fees
        gp_commitment: GP commitment amount
        edited_df: DataFrame with exit plan information
        ipo_schedules: List of IPO distribution schedules (optional)
    """
    if ipo_schedules:
        # Enhanced waterfall section with IPO distribution analysis only
        st.subheader("Fund Exit Value Analysis")
        st.info("🎯 **IPO Scenario Active**: Showing enhanced waterfall with IPO distribution analysis")
        
        st.markdown("#### Traditional Waterfall with IPO Distribution Analysis")
        st.caption("Shows traditional waterfall enhanced with IPO distribution details")
        # IPO distribution view
        _display_traditional_waterfall_with_ipo_distribution(fee_tracker, company_net_investments, data_df, total_value, carry_calculation, include_capital_calls, gp_commitment, edited_df, ipo_schedules)
    else:
        # Standard single waterfall
        st.subheader("Fund Exit Value Waterfall")
        _display_standard_waterfall_chart(fee_tracker, company_net_investments, data_df, total_value, carry_calculation, include_capital_calls, gp_commitment, edited_df)

def _display_standard_waterfall_chart(fee_tracker, company_net_investments, data_df, total_value, carry_calculation, include_capital_calls, gp_commitment, edited_df, chart_key="traditional_waterfall_chart"):
    """
    Displays the traditional fund exit value waterfall chart with capital recovery tracking.
    """
    try:
        if fee_tracker:
            # Import capital recovery tracking
            from capital_recovery_analysis import track_capital_recovery
            
            # Get written off companies
            written_off_companies = edited_df[edited_df["MOIC"] == 0]["Company"].tolist()
            
            # Track capital recovery at each exit
            recovery_metrics = track_capital_recovery(fee_tracker, company_net_investments, written_off_companies)
            
            # Capital recovery summary removed per user request
            # display_capital_recovery_summary(recovery_metrics, written_off_companies)
            
            sorted_exits_wf = sorted(fee_tracker, key=lambda x_wf: x_wf["Exit Date"])
            waterfall_data_wf = []
            
            # Calculate total net investment
            total_investment_wf = sum(company_net_investments.values())
            written_off_capital = sum(company_net_investments.get(comp, 0) for comp in written_off_companies)
            
            waterfall_data_wf.append({
                "Step": "Initial Investment",
                "Value": total_investment_wf,
                "Running_Total": total_investment_wf,
                "Date": None,
                "Capital_Recovered": 0,
                "Written_Off_Capital": written_off_capital,
                "Written_Off_Coverage": 0
            })
            
            grouped_exits_wf = {}
            for exit_info_wf_item in sorted_exits_wf:
                exit_date_wf = exit_info_wf_item["Exit Date"]
                if exit_date_wf not in grouped_exits_wf:
                    grouped_exits_wf[exit_date_wf] = {
                        "Date": exit_date_wf,
                        "Companies": [],
                        "Total Value": 0,
                        "Total Fees": 0,
                        "Management Fees": 0,
                        "Capital Call Fees": 0,
                        "Capital Call PR": 0
                    }
                grouped_exits_wf[exit_date_wf]["Companies"].append(exit_info_wf_item["Company"])
                grouped_exits_wf[exit_date_wf]["Total Value"] += exit_info_wf_item["Exit Value"]
                grouped_exits_wf[exit_date_wf]["Total Fees"] += exit_info_wf_item["Total Fees"]
                
                if include_capital_calls:
                    grouped_exits_wf[exit_date_wf]["Management Fees"] += exit_info_wf_item.get("Management Fees", 0)
                    grouped_exits_wf[exit_date_wf]["Capital Call Fees"] += exit_info_wf_item.get("Capital Call Fees", 0)
                    grouped_exits_wf[exit_date_wf]["Capital Call PR"] += exit_info_wf_item.get("Capital Call PR", 0)
            
            current_running_total_wf = total_investment_wf
            
            company_metrics = {}
            for exit_info in sorted_exits_wf:
                if exit_info["Status"] == "Active" and exit_info["Exit Value"] > 0:
                    comp_name = exit_info["Company"]
                    comp_data = data_df[data_df["Deal Name"] == comp_name]
                    
                    net_inv = company_net_investments.get(comp_name, 0)
                    gross_moic = exit_info["Exit Value"] / net_inv if net_inv > 0 else 0
                    
                    comp_flows = [(row["Date"], row["Distributions"] + row["Contributions"]) for _, row in comp_data.iterrows()]
                    comp_flows.append((exit_info["Exit Date"], exit_info["Exit Value"]))
                    comp_flows_agg = aggregate_cashflows_by_date(comp_flows)
                    gross_irr = xirr(comp_flows_agg)
                    
                    company_metrics[comp_name] = {
                        "gross_moic": gross_moic,
                        "gross_irr": gross_irr * 100 if gross_irr is not None else None
                    }

            # Track cumulative capital recovery
            cumulative_net_proceeds = 0
            capital_recovery_shown = False  # Track if we've already shown the recovery indicator

            for exit_date_wf_proc, info_wf in sorted(grouped_exits_wf.items()):
                companies_wf_list = info_wf["Companies"]
                exit_value_wf = info_wf["Total Value"]
                fees_wf = info_wf["Total Fees"]
                
                exit_date_label_wf = exit_date_wf_proc.strftime('%Y-%m-%d') if pd.notna(exit_date_wf_proc) else 'N/A'
                
                # Calculate capital recovery at this exit
                net_proceeds = exit_value_wf - fees_wf
                cumulative_net_proceeds += net_proceeds
                
                capital_recovered = min(cumulative_net_proceeds, total_investment_wf)
                written_off_coverage = max(0, cumulative_net_proceeds - (total_investment_wf - written_off_capital))
                
                exit_value_numeric = float(exit_value_wf) if isinstance(exit_value_wf, (int, float)) else 0.0
                step_label = "Exit Value" if len(waterfall_data_wf) == 1 else f"Exit<br>({exit_date_label_wf})"
                
                current_running_total_wf += exit_value_numeric
                waterfall_data_wf.append({
                    "Step": step_label, "Value": exit_value_numeric, "Running_Total": current_running_total_wf,
                    "Date": exit_date_wf_proc, "Companies": companies_wf_list,
                    "Capital_Recovered": capital_recovered,
                    "Written_Off_Capital": written_off_capital,
                    "Written_Off_Coverage": written_off_coverage
                })
                
                if include_capital_calls and (info_wf["Management Fees"] > 0 or info_wf["Capital Call Fees"] > 0):
                    if info_wf["Management Fees"] > 0:
                        mgmt_fee_numeric = float(info_wf["Management Fees"])
                        current_running_total_wf -= mgmt_fee_numeric
                        waterfall_data_wf.append({
                            "Step": f"Mgmt Fees<br>({exit_date_label_wf})", "Value": -mgmt_fee_numeric,
                            "Running_Total": current_running_total_wf, "Date": exit_date_wf_proc
                        })

                    if info_wf["Capital Call Fees"] > 0:
                        cap_call_fee_numeric = float(info_wf["Capital Call Fees"])
                        current_running_total_wf -= cap_call_fee_numeric
                        waterfall_data_wf.append({
                            "Step": f"Capital Calls<br>({exit_date_label_wf})", "Value": -cap_call_fee_numeric,
                            "Running_Total": current_running_total_wf, "Date": exit_date_wf_proc
                        })
                    
                    if info_wf.get("Capital Call PR", 0) > 0:
                        pr_numeric = float(info_wf.get("Capital Call PR", 0))
                        current_running_total_wf -= pr_numeric
                        waterfall_data_wf.append({
                            "Step": f"Priority Return<br>({exit_date_label_wf})", "Value": -pr_numeric,
                            "Running_Total": current_running_total_wf, "Date": exit_date_wf_proc
                        })

                elif fees_wf > 0:
                    fees_numeric = float(fees_wf)
                    current_running_total_wf -= fees_numeric
                    waterfall_data_wf.append({
                        "Step": f"Fees<br>({exit_date_label_wf})", "Value": -fees_numeric,
                        "Running_Total": current_running_total_wf, "Date": exit_date_wf_proc
                    })

            current_running_total_wf = total_value
            
            total_exit_value = sum(item.get("Exit Value", 0) for item in fee_tracker)
            total_mgmt_fees_wf = sum(item.get("Management Fees", 0) for item in fee_tracker)
            distributable_value = total_exit_value - total_mgmt_fees_wf
            
            total_capital_call_fees_wf = sum(item.get("Capital Call Fees", 0) for item in fee_tracker)
            total_calls = sum(company_net_investments.values()) + total_capital_call_fees_wf
            
            total_capital_call_pr = sum(item.get("Capital Call PR", 0) for item in fee_tracker)
            total_priority_return = carry_calculation['hurdle_return'] + total_capital_call_pr
            
            remaining_after_calls_and_pr = distributable_value - total_calls - total_priority_return
            gp_catchup_formula_amount = (total_priority_return / 0.8) * 0.2
            gp_catchup_new_formula = max(0, min(remaining_after_calls_and_pr, gp_catchup_formula_amount))
            
            remaining_for_carry = max(0, remaining_after_calls_and_pr - gp_catchup_new_formula)
            gp_carry_new_formula = remaining_for_carry * 0.20
            
            fund_pr = carry_calculation['hurdle_return']
            
            if fund_pr > 0:
                current_running_total_wf -= fund_pr
                waterfall_data_wf.append({
                    "Step": "PR (Fund)", "Value": -fund_pr, "Running_Total": current_running_total_wf, "Date": None
                })
            
            if gp_catchup_new_formula > 0:
                current_running_total_wf -= gp_catchup_new_formula
                waterfall_data_wf.append({
                    "Step": "GP Catchup ", "Value": -gp_catchup_new_formula, "Running_Total": current_running_total_wf, "Date": None
                })

            current_running_total_wf -= gp_carry_new_formula
            waterfall_data_wf.append({
                "Step": "GP Carry (20%)", "Value": -gp_carry_new_formula, "Running_Total": current_running_total_wf, "Date": None
            })
            
            waterfall_data_wf.append({
                "Step": "Final Fund Value", "Value": 0, "Running_Total": current_running_total_wf, "Date": None
            })
            
            waterfall_x_labels_wf = [item["Step"] for item in waterfall_data_wf]
            waterfall_y_values_wf = [item["Value"] for item in waterfall_data_wf]
            waterfall_text_wf = [f'${val:,.0f}' if val != 0 else f'${item["Running_Total"]:,.0f}' for item, val in zip(waterfall_data_wf, waterfall_y_values_wf)]

            fund_moic_val, _, _ = calculate_fund_moic(data_df, fee_tracker, edited_df, gp_commitment, company_net_investments)
            
            fig = go.Figure(go.Waterfall(
                name="Fund Value Waterfall", orientation="v",
                measure=["relative"] * (len(waterfall_data_wf) - 1) + ["total"],
                x=waterfall_x_labels_wf, y=waterfall_y_values_wf, text=waterfall_text_wf,
                textposition="outside", connector={"line": {"color": "rgb(63, 63, 63)"}},
                increasing={"marker": {"color": "green"}}, decreasing={"marker": {"color": "red"}},
                totals={"marker": {"color": "purple"}}
            ))
            
            # Calculate y-axis range to include annotations
            max_y = max([item["Running_Total"] for item in waterfall_data_wf])
            y_range_padding = max_y * 0.4  # Increased padding for capital recovery annotations
            y_axis_max = max_y + y_range_padding
            
            fig.update_layout(
                title_text="Fund Exit Value Waterfall with Capital Recovery",
                yaxis_title='Value ($)', height=1100, showlegend=False,  # Increased height
                yaxis_gridcolor='rgba(0,0,0,0.1)', yaxis_gridwidth=1,
                xaxis_tickangle=0, margin=dict(b=150, t=300, l=80, r=80),  # Increased top margin
                yaxis=dict(range=[0, y_axis_max])
            )
            
            # Capital recovery milestone markers removed per user request
            # add_capital_recovery_milestones(fig, waterfall_data_wf, total_investment_wf, written_off_capital, y_axis_max)
            
            for i, item in enumerate(waterfall_data_wf):
                if "Companies" in item and item["Companies"] and item["Step"] != "Exit Value":
                    num_companies = len(item["Companies"])
                    # Limit annotations to prevent overcrowding
                    max_annotations_per_exit = 4
                    companies_to_annotate = item["Companies"][:max_annotations_per_exit]
                    
                    for j, company in enumerate(companies_to_annotate):
                        if company in company_metrics:
                            metrics = company_metrics[company]
                            annotation_text = f"<b>{company}</b><br>MOIC: {metrics['gross_moic']:.2f}x<br>"
                            annotation_text += f"IRR: {metrics['gross_irr']:.1f}%" if metrics['gross_irr'] is not None else "IRR: N/A"
                            
                            # Adjust spacing based on number of companies
                            if num_companies > 1:
                                x_offset = (j - len(companies_to_annotate)/2 + 0.5) * 0.15
                            else:
                                x_offset = 0
                            
                            # Stagger annotations vertically to prevent overlap
                            y_offset = -80 - (j * 35)
                            # Wrap around if too many annotations
                            if j >= 3:
                                y_offset = -80 - ((j % 3) * 35)
                                x_offset += 0.2 * (j // 3)
                            
                            y_paper_coord = item["Running_Total"] / y_axis_max if y_axis_max > 0 else 0
                            fig.add_annotation(
                                x=i + x_offset, y=y_paper_coord, yref='paper', text=annotation_text, showarrow=True,
                                arrowhead=2, arrowcolor="black", arrowwidth=1, ax=0, ay=y_offset,
                                font=dict(size=9, color="black"), bgcolor="rgba(255, 255, 255, 0.8)",
                                bordercolor="black", borderwidth=1, opacity=0.9,
                                xanchor="center", yanchor="top"
                            )
                    
                    # Add capital recovery annotation - show only when running total crosses zero (becomes positive)
                    running_total = item["Running_Total"]

                    # Check if this is the point where capital is fully recovered (running total becomes positive)
                    if running_total > 0 and not capital_recovery_shown:
                        # Check if previous running total was negative (to ensure this is the crossing point)
                        if i == 0 or (i > 0 and waterfall_data_wf[i-1]["Running_Total"] <= 0):
                            recovery_text = "✅ Full Capital Recovered"
                            recovery_color = "green"
                            capital_recovery_shown = True

                            # Add capital recovery annotation
                            y_paper_coord = running_total / y_axis_max if y_axis_max > 0 else 0
                            fig.add_annotation(
                                x=i, y=y_paper_coord, yref='paper',
                                text=recovery_text,
                                showarrow=True, arrowhead=2, arrowcolor=recovery_color,
                                ax=80, ay=-160,
                                font=dict(size=12, color=recovery_color, family="Arial", weight="bold"),
                                bgcolor="rgba(255, 255, 255, 0.9)",
                                bordercolor=recovery_color, borderwidth=2,
                                xanchor="center", yanchor="top"
                            )
                        
                        # Written-off capital coverage annotations removed per user request
                        # if written_off_capital > 0:
                        #     if written_off_coverage >= written_off_capital:
                        #         writeoff_text = f"✅ Written-off Capital Recovered<br>(${written_off_capital:,.0f})"
                        #         writeoff_color = "green"
                        #     elif written_off_coverage > 0:
                        #         writeoff_pct = (written_off_coverage / written_off_capital * 100) if written_off_capital > 0 else 0
                        #         writeoff_text = f"📈 {writeoff_pct:.0f}% Written-off Coverage<br>(${written_off_coverage:,.0f} of ${written_off_capital:,.0f})"
                        #         writeoff_color = "red"
                        #     else:
                        #         writeoff_text = f"❌ No Written-off Coverage<br>(${written_off_capital:,.0f} at risk)"
                        #         writeoff_color = "darkred"
                        #     
                        #     fig.add_annotation(
                        #         x=i, y=y_paper_coord, yref='paper',
                        #         text=writeoff_text,
                        #         showarrow=True, arrowhead=2, arrowcolor=writeoff_color,
                        #         ax=-80, ay=-160,
                        #         font=dict(size=10, color=writeoff_color, family="Arial"),
                        #         bgcolor="rgba(255, 255, 255, 0.9)",
                        #         bordercolor=writeoff_color, borderwidth=1,
                        #         xanchor="center", yanchor="top"
                        #     )
                    
                    # Add a note if there are more companies than shown
                    if num_companies > max_annotations_per_exit:
                        y_paper_coord = item["Running_Total"] / y_axis_max if y_axis_max > 0 else 0
                        fig.add_annotation(
                            x=i, y=y_paper_coord, yref='paper',
                            text=f"<i>+{num_companies - max_annotations_per_exit} more companies</i>",
                            showarrow=False, ay=-240,
                            font=dict(size=8, color="gray"),
                            xanchor="center", yanchor="top"
                        )
            
            if waterfall_data_wf and fund_moic_val is not None:
                final_total = waterfall_data_wf[-1]["Running_Total"]
                y_paper_coord = final_total / y_axis_max if y_axis_max > 0 else 0
                fig.add_annotation(
                    x=waterfall_x_labels_wf[-1], y=y_paper_coord, yref='paper', text=f'MOIC: {fund_moic_val:.2f}x',
                    showarrow=True, arrowhead=1, ax=0, ay=-40, font=dict(size=12, color="black"),
                    bgcolor="yellow", opacity=0.7
                )
            
            st.plotly_chart(fig, use_container_width=True, key=chart_key)
            
            # Store the figure object itself in session state for later export
            st.session_state["fund_waterfall_figure"] = fig
            st.session_state["fund_waterfall_data"] = {
                "waterfall_data_wf": waterfall_data_wf,
                "waterfall_x_labels_wf": waterfall_x_labels_wf,
                "waterfall_y_values_wf": waterfall_y_values_wf,
                "waterfall_text_wf": waterfall_text_wf
            }

            # Capture figure for PDF export - TEMPORARILY DISABLED to prevent hanging
            try:
                # Store the figure for potential later use
                st.session_state["fund_waterfall_figure"] = fig
                
                # Disable automatic chart export to prevent hanging
                st.session_state["fund_waterfall_chart_svg_b64"] = None
                st.session_state["fund_waterfall_chart_b64"] = None
                
                # Show status that chart export is disabled
                with st.expander("Chart Export Status", expanded=False):
                    st.warning("⚠️ Chart export temporarily disabled to prevent app hanging. PDF will not include waterfall chart.")
                
                print("DEBUG: Chart export disabled to prevent hanging")
                
            except Exception as e:
                print(f"DEBUG: Error in waterfall chart section: {e}")
                st.session_state["fund_waterfall_chart_svg_b64"] = None
                st.session_state["fund_waterfall_chart_b64"] = None

            except Exception as err_img_gen:
                # If generation fails (e.g., Kaleido missing or other Plotly issue)
                # ensure session state variables for chart data are None.
                st.session_state["fund_waterfall_chart_svg_b64"] = None
                st.session_state["fund_waterfall_chart_b64"] = None
                st.warning(f"Chart image generation for PDF failed: {err_img_gen}")
                print(f"DEBUG: Outer exception in chart generation: {err_img_gen}")
                print(f"DEBUG: Full traceback: {traceback.format_exc()}")
        else:
            st.info("No exits planned. Configure your exit plan to see the waterfall chart.")
            # Ensure we clear any old chart data when no exits are planned
            st.session_state["fund_waterfall_chart_svg_b64"] = None
            st.session_state["fund_waterfall_chart_b64"] = None
    except Exception as e:
        st.error(f"Error creating waterfall chart: {e}")
        # Consider logging traceback to a file or a more advanced logging system
        # instead of st.expander for production.
        # For now, keeping it simple:
        print(f"Waterfall chart error: {traceback.format_exc()}")

def _display_ipo_timeline_view(ipo_schedules, edited_df, company_net_investments):
    """
    Display IPO distribution timeline using waterfall charts and analytics.
    """
    try:
        # Create IPO distribution waterfall chart
        st.markdown("##### IPO Distribution Waterfall Timeline")
        ipo_waterfall_fig = _create_ipo_distribution_waterfall(ipo_schedules, edited_df)
        if ipo_waterfall_fig:
            st.plotly_chart(ipo_waterfall_fig, use_container_width=True, key="ipo_timeline_waterfall_chart")
        else:
            st.info("No IPO distribution waterfall to display")
        
        # Show distribution summary table
        st.markdown("##### 📋 Distribution Summary")
        
        # Create comprehensive summary table
        summary_data = []
        for schedule in ipo_schedules:
            company_name = schedule['company_name']
            total_distributions = len(schedule['distribution_dates'])
            exit_value = schedule.get('exit_value', 0)
            total_gross = sum(schedule['distribution_amounts'])
            total_fees = schedule.get('total_fees', 0)
            total_net = sum(schedule.get('distribution_amounts_after_fees', schedule['distribution_amounts']))
            pattern = schedule.get('pattern', 'equal')
            frequency = schedule.get('frequency', 'semi-annual')
            first_date = schedule['distribution_dates'][0] if schedule['distribution_dates'] else None
            last_date = schedule['distribution_dates'][-1] if schedule['distribution_dates'] else None
            
            summary_data.append({
                'Company': company_name,
                'Exit Value': f"${exit_value:,.0f}",
                'Pattern': pattern.title(),
                'Frequency': frequency.replace('-', ' ').title(),
                '# Distributions': total_distributions,
                'Gross Distributions': f"${total_gross:,.0f}",
                'Total Fees': f"${total_fees:,.0f}" if total_fees > 0 else "-",
                'Net to LPs': f"${total_net:,.0f}",
                'First Distribution': first_date.strftime('%Y-%m-%d') if first_date else 'N/A',
                'Last Distribution': last_date.strftime('%Y-%m-%d') if last_date else 'N/A',
                'Period (Months)': f"{((last_date - first_date).days / 30.44):.1f}" if first_date and last_date else 'N/A'
            })
        
        if summary_data:
            import pandas as pd
            summary_df = pd.DataFrame(summary_data)
            st.dataframe(summary_df, use_container_width=True)
        
        # Optional: Also show the Gantt chart in an expander for timeline view
        with st.expander("📅 Timeline View (Gantt Chart)", expanded=False):
            try:
                from ipo_scenario_analytics import create_distribution_timeline_gantt
                gantt_fig = create_distribution_timeline_gantt(ipo_schedules, edited_df)
                if gantt_fig:
                    st.plotly_chart(gantt_fig, use_container_width=True)
                else:
                    st.info("No distribution timeline to display")
            except Exception as e:
                st.error(f"Error creating Gantt chart: {str(e)}")
        
        # Show detailed distribution schedule
        with st.expander("📊 Detailed Distribution Schedule", expanded=False):
            # Create comprehensive detailed view with fee breakdown
            detailed_data = []
            
            for schedule in ipo_schedules:
                company_name = schedule['company_name']
                has_fees = 'distribution_amounts_after_fees' in schedule
                is_first_exit = schedule.get('is_first_exit', False)
                exit_value = schedule.get('exit_value', 0)
                
                for i, (date, gross_amt, pct) in enumerate(zip(
                    schedule['distribution_dates'],
                    schedule['distribution_amounts'],
                    schedule.get('distribution_percentages', [0] * len(schedule['distribution_dates']))
                )):
                    if has_fees:
                        net_amt = schedule['distribution_amounts_after_fees'][i]
                        mgmt_fee = schedule.get('management_fee_amounts', [0] * len(schedule['distribution_dates']))[i]
                        cc_fee = schedule.get('capital_call_fee_amounts', [0] * len(schedule['distribution_dates']))[i]
                        pr_fee = schedule.get('priority_return_amounts', [0] * len(schedule['distribution_dates']))[i]
                        total_fee = mgmt_fee + cc_fee + pr_fee
                    else:
                        net_amt = gross_amt
                        mgmt_fee = cc_fee = pr_fee = total_fee = 0
                    
                    # Build fee detail string
                    fee_details = []
                    if mgmt_fee > 0:
                        fee_details.append(f"Mgmt: ${mgmt_fee:,.0f}")
                    if cc_fee > 0:
                        fee_details.append(f"CC: ${cc_fee:,.0f}")
                    if pr_fee > 0:
                        fee_details.append(f"PR: ${pr_fee:,.0f}")
                    
                    fee_detail_str = " | ".join(fee_details) if fee_details else "-"
                    
                    # Determine if this is an exit event (first distribution)
                    event_type = "IPO Exit + Dist 1" if i == 0 else f"Distribution {i + 1}"
                    
                    detailed_data.append({
                        'Company': company_name,
                        'Event': event_type,
                        'Date': date.strftime('%Y-%m-%d'),
                        'Exit Value': f"${exit_value:,.0f}" if i == 0 else "-",
                        'Gross Amount': f"${gross_amt:,.0f}",
                        'Management Fees': f"${mgmt_fee:,.0f}" if mgmt_fee > 0 else "-",
                        'Capital Call Fees': f"${cc_fee:,.0f}" if cc_fee > 0 else "-",
                        'Priority Return': f"${pr_fee:,.0f}" if pr_fee > 0 else "-",
                        'Total Fees': f"${total_fee:,.0f}" if total_fee > 0 else "-",
                        'Net Amount': f"${net_amt:,.0f}",
                        'Percentage': f"{pct:.1f}%" if pct > 0 else "-",
                        'Fee Details': fee_detail_str,
                        'First Exit': "Yes" if is_first_exit and i == 0 else "No"
                    })
            
            if detailed_data:
                detailed_df = pd.DataFrame(detailed_data)
                st.dataframe(detailed_df, use_container_width=True)
                
                # Display fee summary if any fees exist
                total_mgmt_fees = sum([schedule.get('total_management_fees', 0) for schedule in ipo_schedules])
                total_cc_fees = sum([schedule.get('total_capital_call_fees', 0) for schedule in ipo_schedules])
                total_pr_fees = sum([schedule.get('total_priority_return', 0) for schedule in ipo_schedules])
                total_all_fees = total_mgmt_fees + total_cc_fees + total_pr_fees
                
                if total_all_fees > 0:
                    st.markdown("**Fee Summary:**")
                    fee_cols = st.columns(4)
                    with fee_cols[0]:
                        st.metric("Management Fees", f"${total_mgmt_fees:,.0f}")
                    with fee_cols[1]:
                        st.metric("Capital Call Fees", f"${total_cc_fees:,.0f}")
                    with fee_cols[2]:
                        st.metric("Priority Return", f"${total_pr_fees:,.0f}")
                    with fee_cols[3]:
                        st.metric("Total Fees", f"${total_all_fees:,.0f}")
                    
                    # Fee allocation explanation
                    st.caption("🔹 **Decremental Allocation**: Management fees accrue quarterly but decrease as shares are distributed over time")
            else:
                st.info("No detailed distribution data available")
        

        
    except Exception as e:
        st.error(f"Error displaying IPO timeline view: {str(e)}")
        print(f"IPO timeline view error: {traceback.format_exc()}")

def _create_ipo_distribution_waterfall(ipo_schedules, edited_df):
    """
    Create a waterfall chart showing IPO distribution timeline.
    Shows how exit values flow through distributions over time.
    """
    try:
        import plotly.graph_objects as go
        import pandas as pd
        from datetime import datetime
        
        if not ipo_schedules:
            return None
        
        # Collect all distribution events with company info
        waterfall_data = []
        all_events = []
        
        # Add initial exit values
        for schedule in ipo_schedules:
            company_name = schedule['company_name']
            exit_value = sum(schedule.get('distribution_amounts_after_fees', schedule['distribution_amounts']))
            
            # Find exit date from edited_df
            company_row = edited_df[edited_df['Company'] == company_name]
            if not company_row.empty:
                exit_date = pd.to_datetime(company_row['Exit Date'].iloc[0])
            else:
                exit_date = schedule['distribution_dates'][0] if schedule['distribution_dates'] else datetime.now()
            
            waterfall_data.append({
                'Step': f"{company_name}<br>IPO Exit",
                'Value': exit_value,
                'Date': exit_date,
                'Type': 'exit',
                'Company': company_name
            })
            
            # Add all distribution events
            for i, (dist_date, amount) in enumerate(zip(
                schedule['distribution_dates'],
                schedule.get('distribution_amounts_after_fees', schedule['distribution_amounts'])
            )):
                all_events.append({
                    'Step': f"{company_name}<br>Dist {i+1}",
                    'Value': -amount,  # Negative for distributions (money flowing out)
                    'Date': dist_date,
                    'Type': 'distribution',
                    'Company': company_name,
                    'Distribution_Number': i + 1
                })
        
        # Sort events by date
        all_events.sort(key=lambda x: x['Date'])
        
        # Combine exits and distributions in chronological order
        combined_data = []
        
        # Add total initial value
        total_exit_value = sum(item['Value'] for item in waterfall_data)
        combined_data.append({
            'Step': 'Total IPO<br>Exit Value',
            'Value': total_exit_value,
            'Date': None,
            'Type': 'initial'
        })
        
        # Add distributions chronologically
        for event in all_events:
            combined_data.append(event)
        
        # Add final remaining value (should be 0 for full distributions)
        final_value = total_exit_value + sum(event['Value'] for event in all_events)
        combined_data.append({
            'Step': 'Remaining<br>Value',
            'Value': 0,  # This will be calculated by plotly as total
            'Date': None,
            'Type': 'final'
        })
        
        # Create waterfall chart
        x_labels = [item['Step'] for item in combined_data]
        y_values = [item['Value'] for item in combined_data]
        
        # Create text labels with better formatting
        text_labels = []
        for item, value in zip(combined_data, y_values):
            if item['Type'] == 'initial':
                text_labels.append(f'${value:,.0f}')
            elif item['Type'] == 'distribution':
                text_labels.append(f'${abs(value):,.0f}')
            elif item['Type'] == 'final':
                text_labels.append('$0')
            else:
                text_labels.append(f'${value:,.0f}')
        
        # Determine measure types for waterfall
        measures = []
        for item in combined_data:
            if item['Type'] == 'initial':
                measures.append('absolute')
            elif item['Type'] == 'final':
                measures.append('total')
            else:
                measures.append('relative')
        
        # Create color mapping
        colors = []
        for item in combined_data:
            if item['Type'] == 'initial':
                colors.append('#2E86C1')  # Blue for initial
            elif item['Type'] == 'distribution':
                colors.append('#E74C3C')  # Red for distributions (outflow)
            elif item['Type'] == 'final':
                colors.append('#8E44AD')  # Purple for final
            else:
                colors.append('#28B463')  # Green for other
        
        fig = go.Figure(go.Waterfall(
            name="IPO Distribution Timeline",
            orientation="v",
            measure=measures,
            x=x_labels,
            y=y_values,
            text=text_labels,
            textposition="outside",
            connector={"line": {"color": "rgb(63, 63, 63)"}},
            increasing={"marker": {"color": "#28B463"}},
            decreasing={"marker": {"color": "#E74C3C"}},
            totals={"marker": {"color": "#8E44AD"}}
        ))
        
        # Add annotations for dates
        for i, item in enumerate(combined_data):
            if item['Date'] and item['Type'] == 'distribution':
                fig.add_annotation(
                    x=i,
                    y=0,
                    text=item['Date'].strftime('%Y-%m'),
                    showarrow=False,
                    yshift=-40,
                    font=dict(size=9, color="gray"),
                    textangle=-45
                )
        
        # Update layout
        fig.update_layout(
            title="IPO Distribution Waterfall Timeline",
            xaxis_title="Distribution Events",
            yaxis_title="Value ($)",
            height=600,
            showlegend=False,
            yaxis=dict(tickformat='$,.0f'),
            xaxis=dict(tickangle=-45),
            margin=dict(b=150, t=100, l=80, r=80)
        )
        
        return fig
        
    except Exception as e:
        print(f"Error creating IPO distribution waterfall: {e}")
        print(f"Full traceback: {traceback.format_exc()}")
        return None


def _display_traditional_waterfall_with_ipo_distribution(fee_tracker, company_net_investments, data_df, total_value, carry_calculation, include_capital_calls, gp_commitment, edited_df, ipo_schedules):
    """
    Displays the traditional waterfall chart enhanced with IPO distribution information.
    Shows only the enhanced waterfall with IPO distributions.
    """
    st.markdown("##### 🏗️ Traditional Waterfall Enhanced with IPO Distributions")
    
    # Display only the enhanced waterfall with IPO distributions
    _display_enhanced_waterfall_with_ipo_additions(fee_tracker, company_net_investments, data_df, total_value, carry_calculation, include_capital_calls, gp_commitment, edited_df, ipo_schedules)



def _display_integrated_waterfall_with_ipo_timeline(fee_tracker, company_net_investments, data_df, total_value, carry_calculation, include_capital_calls, gp_commitment, edited_df, ipo_schedules):
    """
    Creates an integrated waterfall chart that combines traditional fund waterfall with IPO distribution timeline.
    Shows how fund value flows through IPO distributions over time.
    """
    try:
        import plotly.graph_objects as go
        import pandas as pd
        from datetime import datetime
        
        if not ipo_schedules:
            st.info("No IPO distribution schedules available for integrated waterfall.")
            return
        
        # Calculate total net investment
        total_investment = sum(company_net_investments.values())
        
        # Create integrated waterfall data
        waterfall_data = []
        
        # Start with initial investment
        waterfall_data.append({
            "Step": "Initial<br>Investment",
            "Value": total_investment,
            "Running_Total": total_investment,
            "Type": "initial",
            "Date": None
        })
        
        # Add total exit value (excluding IPO companies)
        # Get IPO company names to exclude from total exit value
        ipo_company_names = [schedule['company_name'] for schedule in ipo_schedules]
        
        # Calculate total exit value excluding IPO companies
        total_exit_value = sum(item.get("Exit Value", 0) for item in fee_tracker 
                             if item.get("Company") not in ipo_company_names)
        exit_value_premium = total_exit_value - total_investment
        current_running_total = total_exit_value
        
        waterfall_data.append({
            "Step": "Total Exit Value<br>(exc. IPO)",
            "Value": exit_value_premium,
            "Running_Total": current_running_total,
            "Type": "exit_value",
            "Date": None
        })
        
        # Collect all IPO distribution events with dates
        distribution_events = []
        
        for schedule in ipo_schedules:
            company_name = schedule['company_name']
            distribution_dates = schedule['distribution_dates']
            distribution_amounts = schedule.get('distribution_amounts_after_fees', schedule['distribution_amounts'])
            
            for i, (date, amount) in enumerate(zip(distribution_dates, distribution_amounts)):
                distribution_events.append({
                    'date': date,
                    'company': company_name,
                    'amount': amount,
                    'distribution_number': i + 1,
                    'total_distributions': len(distribution_dates)
                })
        
        # Sort distributions by date
        distribution_events.sort(key=lambda x: x['date'])
        
        # Add each distribution chronologically (these are positive returns to LPs)
        for event in distribution_events:
            date_str = event['date'].strftime('%Y-%m-%d')
            company = event['company']
            dist_num = event['distribution_number']
            total_dists = event['total_distributions']
            
            # Step label with company and distribution info
            step_label = f"{company}<br>Dist {dist_num}/{total_dists}<br>({date_str})"
            
            # Distributions are positive returns to LPs
            current_running_total += event['amount']
            
            waterfall_data.append({
                "Step": step_label,
                "Value": event['amount'],  # Positive because it's value being returned to LPs
                "Running_Total": current_running_total,
                "Type": "distribution",
                "Date": event['date'],
                "Company": company
            })
        
        # Calculate traditional waterfall fees (excluding IPO companies)
        total_mgmt_fees = sum(item.get("Management Fees", 0) for item in fee_tracker 
                             if item.get("Company") not in ipo_company_names)
        total_capital_call_fees = sum(item.get("Capital Call Fees", 0) for item in fee_tracker 
                                    if item.get("Company") not in ipo_company_names)
        total_capital_call_pr = sum(item.get("Capital Call PR", 0) for item in fee_tracker 
                                  if item.get("Company") not in ipo_company_names)
        
        # Add management fees
        if total_mgmt_fees > 0:
            current_running_total -= total_mgmt_fees
            waterfall_data.append({
                "Step": "Management<br>Fees",
                "Value": -total_mgmt_fees,
                "Running_Total": current_running_total,
                "Type": "fee",
                "Date": None
            })
        
        # Add capital call fees if applicable
        if include_capital_calls and total_capital_call_fees > 0:
            current_running_total -= total_capital_call_fees
            waterfall_data.append({
                "Step": "Capital Call<br>Fees",
                "Value": -total_capital_call_fees,
                "Running_Total": current_running_total,
                "Type": "fee",
                "Date": None
            })
        
        # Add capital call priority return if applicable
        if include_capital_calls and total_capital_call_pr > 0:
            current_running_total -= total_capital_call_pr
            waterfall_data.append({
                "Step": "Capital Call<br>Priority Return",
                "Value": -total_capital_call_pr,
                "Running_Total": current_running_total,
                "Type": "fee",
                "Date": None
            })
        
        # Add fund-level priority return
        fund_pr = carry_calculation.get('hurdle_return', 0)
        if fund_pr > 0:
            current_running_total -= fund_pr
            waterfall_data.append({
                "Step": "Fund<br>Priority Return",
                "Value": -fund_pr,
                "Running_Total": current_running_total,
                "Type": "fee",
                "Date": None
            })
        
        # Calculate GP catchup and carry
        distributable_value = total_exit_value - total_mgmt_fees
        total_calls = sum(company_net_investments.values()) + total_capital_call_fees
        total_priority_return = fund_pr + total_capital_call_pr
        
        remaining_after_calls_and_pr = distributable_value - total_calls - total_priority_return
        gp_catchup_amount = (total_priority_return / 0.8) * 0.2
        gp_catchup = max(0, min(remaining_after_calls_and_pr, gp_catchup_amount))
        
        if gp_catchup > 0:
            current_running_total -= gp_catchup
            waterfall_data.append({
                "Step": "GP<br>Catchup",
                "Value": -gp_catchup,
                "Running_Total": current_running_total,
                "Type": "fee",
                "Date": None
            })
        
        # Calculate GP carry
        remaining_for_carry = max(0, remaining_after_calls_and_pr - gp_catchup)
        gp_carry = remaining_for_carry * 0.20
        
        if gp_carry > 0:
            current_running_total -= gp_carry
            waterfall_data.append({
                "Step": "GP Carry<br>(20%)",
                "Value": -gp_carry,
                "Running_Total": current_running_total,
                "Type": "fee",
                "Date": None
            })
        
        # Final fund value
        waterfall_data.append({
            "Step": "Final<br>Fund Value",
            "Value": 0,
            "Running_Total": current_running_total,
            "Type": "final",
            "Date": None
        })
        
        # Create waterfall chart
        x_labels = [item["Step"] for item in waterfall_data]
        y_values = [item["Value"] for item in waterfall_data]
        
        # Create text labels
        text_labels = []
        for item, value in zip(waterfall_data, y_values):
            if value == 0:
                text_labels.append(f'${item["Running_Total"]:,.0f}')
            elif item["Type"] == "exit_value":
                # For exit value, show the total exit value amount
                text_labels.append(f'${item["Running_Total"]:,.0f}')
            elif item["Type"] == "distribution":
                # For distributions, show the positive amount (value returned to LPs)
                text_labels.append(f'${value:,.0f}')
            else:
                text_labels.append(f'${value:,.0f}')
        
        # Determine measure types
        measures = []
        for i, item in enumerate(waterfall_data):
            if item["Type"] == "initial":
                measures.append("absolute")
            elif item["Type"] == "exit_value":
                measures.append("relative")  # Show exit value as relative to initial investment
            elif item["Type"] == "final":
                measures.append("total")
            else:
                measures.append("relative")
        
        # Create the waterfall chart
        fig = go.Figure(go.Waterfall(
            name="Integrated Waterfall with IPO Timeline",
            orientation="v",
            measure=measures,
            x=x_labels,
            y=y_values,
            text=text_labels,
            textposition="outside",
            connector={"line": {"color": "rgb(63, 63, 63)"}},
            increasing={"marker": {"color": "#28B463"}},  # Green for exit value increases
            decreasing={"marker": {"color": "#E74C3C"}},  # Red for distributions and fees (outflows)
            totals={"marker": {"color": "#8E44AD"}}       # Purple for totals
        ))
        
        # Add date annotations for distributions
        for i, item in enumerate(waterfall_data):
            if item["Type"] == "distribution" and item["Date"]:
                fig.add_annotation(
                    x=i,
                    y=0,
                    text=f"{item['Date'].strftime('%m/%d/%y')}",
                    showarrow=False,
                    yshift=-35,
                    font=dict(size=8, color="gray"),
                    textangle=-45
                )
        
        # Update layout
        fig.update_layout(
            title="Traditional Waterfall: Initial Investment → Total Exit Value (exc. IPO) → IPO Distribution Timeline",
            xaxis_title="Fund Flow Events",
            yaxis_title="Value ($)",
            height=700,
            showlegend=False,
            yaxis=dict(tickformat='$,.0f'),
            xaxis=dict(tickangle=-45),
            margin=dict(b=120, t=100, l=80, r=80)
        )
        
        # Display the chart
        st.plotly_chart(fig, use_container_width=True, key="integrated_waterfall_with_ipo_timeline")
        
        # Add summary metrics below the chart
        st.markdown("##### 📊 Integrated Waterfall Summary")
        
        col1, col2, col3, col4, col5 = st.columns(5)
        
        with col1:
            st.metric("Initial Investment", f"${total_investment:,.0f}")
            
        with col2:
            st.metric("Total Exit Value (exc. IPO)", f"${total_exit_value:,.0f}")
            
        with col3:
            total_distributions = sum(event['amount'] for event in distribution_events)
            st.metric("IPO Distributions", f"${total_distributions:,.0f}")
            
        with col4:
            total_fees = total_mgmt_fees + total_capital_call_fees + total_capital_call_pr + fund_pr + gp_catchup + gp_carry
            st.metric("Total Fees", f"${total_fees:,.0f}")
            
        with col5:
            st.metric("Final Fund Value", f"${current_running_total:,.0f}")
        
        # Timeline summary
        if distribution_events:
            first_dist = min(event['date'] for event in distribution_events)
            last_dist = max(event['date'] for event in distribution_events)
            timeline_days = (last_dist - first_dist).days
            
            st.markdown("##### ⏰ Distribution Timeline")
            
            timeline_col1, timeline_col2, timeline_col3 = st.columns(3)
            
            with timeline_col1:
                st.metric("First Distribution", first_dist.strftime("%Y-%m-%d"))
                
            with timeline_col2:
                st.metric("Last Distribution", last_dist.strftime("%Y-%m-%d"))
                
            with timeline_col3:
                st.metric("Distribution Period", f"{timeline_days} days")
        
    except Exception as e:
        st.error(f"Error creating integrated waterfall with IPO timeline: {str(e)}")
        print(f"Integrated waterfall error: {traceback.format_exc()}")

def _display_enhanced_waterfall_with_ipo_additions(fee_tracker, company_net_investments, data_df, total_value, carry_calculation, include_capital_calls, gp_commitment, edited_df, ipo_schedules):
    """
    Creates an enhanced waterfall that starts with the original waterfall data and adds IPO distributions.
    """
    try:
        import plotly.graph_objects as go
        import pandas as pd
        from capital_recovery_analysis import track_capital_recovery
        
        # Get the original waterfall data first (copy logic from _display_standard_waterfall_chart)
        written_off_companies = edited_df[edited_df["MOIC"] == 0]["Company"].tolist()
        recovery_metrics = track_capital_recovery(fee_tracker, company_net_investments, written_off_companies, ipo_schedules)
        
        sorted_exits_wf = sorted(fee_tracker, key=lambda x_wf: x_wf["Exit Date"])
        waterfall_data_wf = []
        
        # Calculate total net investment
        total_investment_wf = sum(company_net_investments.values())
        written_off_capital = sum(company_net_investments.get(comp, 0) for comp in written_off_companies)
        
        # Start with original waterfall structure
        waterfall_data_wf.append({
            "Step": "Initial Investment",
            "Value": total_investment_wf,
            "Running_Total": total_investment_wf,
            "Date": None,
            "Type": "initial",
            "Capital_Recovered": 0,
            "Written_Off_Capital": written_off_capital,
            "Written_Off_Coverage": 0
        })

        current_running_total_wf = total_investment_wf
        cumulative_gross_proceeds = 0  # Track cumulative gross proceeds
        cumulative_net_proceeds = 0   # Track cumulative net proceeds for capital recovery
        
        # Create a combined chronological list of ALL events (regular exits + IPO distributions)
        all_events = []
        ipo_company_names = [schedule['company_name'] for schedule in ipo_schedules] if ipo_schedules else []

        # Add regular exits (excluding IPO companies)
        for exit_info in sorted_exits_wf:
            if exit_info["Status"] == "Active" and exit_info["Exit Value"] > 0:
                company_name = exit_info["Company"]

                # Skip IPO companies - they'll be handled as distributions
                if company_name in ipo_company_names:
                    continue

                all_events.append({
                    'date': pd.to_datetime(exit_info["Exit Date"]),
                    'type': 'regular_exit',
                    'company': company_name,
                    'value': exit_info["Exit Value"],
                    'fees': exit_info.get("Total Fees", 0),
                    'exit_info': exit_info
                })

        # Add IPO distributions
        if ipo_schedules:
            for schedule in ipo_schedules:
                company_name = schedule['company_name']
                distribution_dates = schedule['distribution_dates']
                distribution_amounts = schedule.get('distribution_amounts_after_fees', schedule['distribution_amounts'])

                for i, (date, amount) in enumerate(zip(distribution_dates, distribution_amounts)):
                    all_events.append({
                        'date': pd.to_datetime(date),
                        'type': 'ipo_distribution',
                        'company': company_name,
                        'value': amount,
                        'fees': 0,  # IPO distributions are already net
                        'distribution_number': i + 1,
                        'total_distributions': len(distribution_dates)
                    })

        # Sort ALL events chronologically by date
        all_events.sort(key=lambda x: x['date'])

        # Calculate total fees that will be deducted at the end
        total_mgmt_fees = sum(item.get("Management Fees", 0) for item in fee_tracker)
        total_capital_call_fees = sum(item.get("Capital Call Fees", 0) for item in fee_tracker)
        total_capital_call_pr = sum(item.get("Capital Call PR", 0) for item in fee_tracker)
        fund_pr = carry_calculation.get('hurdle_return', 0)
        total_all_fees = total_mgmt_fees + total_capital_call_fees + total_capital_call_pr + fund_pr

        # Process events in chronological order
        for event in all_events:
            # Calculate cumulative gross proceeds (before fees)
            gross_proceeds = event['value'] - event['fees']
            cumulative_gross_proceeds += gross_proceeds

            # Capital recovery is based on net proceeds AFTER all fees are deducted
            # This matches the waterfall logic where fees come at the end
            cumulative_net_proceeds = cumulative_gross_proceeds - total_all_fees

            # Capital recovered cannot be negative and is capped at total investment
            capital_recovered = max(0, min(cumulative_net_proceeds, total_investment_wf))
            written_off_coverage = max(0, cumulative_net_proceeds - (total_investment_wf - written_off_capital))

            current_running_total_wf += event['value']

            if event['type'] == 'regular_exit':
                date_label = event['date'].strftime("%m/%d/%Y")
                step_label = f"Exit<br>({date_label})"
                event_type = "exit"
            else:  # ipo_distribution
                date_label = event['date'].strftime("%m/%d/%Y")
                dist_num = event['distribution_number']
                step_label = f"{event['company']}<br>IPO Dist {dist_num}<br>({date_label})"
                event_type = "ipo_distribution"

            waterfall_data_wf.append({
                "Step": step_label,
                "Value": event['value'],
                "Running_Total": current_running_total_wf,
                "Date": event['date'],
                "Type": event_type,
                "Company": event['company'],
                "Capital_Recovered": capital_recovered,
                "Written_Off_Capital": written_off_capital,
                "Written_Off_Coverage": written_off_coverage
            })
        
        # Add fees following the original waterfall structure (using already calculated totals)
        
        # Add management fees
        if total_mgmt_fees > 0:
            current_running_total_wf -= total_mgmt_fees
            waterfall_data_wf.append({
                "Step": "Management<br>Fees",
                "Value": -total_mgmt_fees,
                "Running_Total": current_running_total_wf,
                "Type": "fee"
            })
        
        # Add capital call fees if applicable
        if include_capital_calls and total_capital_call_fees > 0:
            current_running_total_wf -= total_capital_call_fees
            waterfall_data_wf.append({
                "Step": "Capital Call<br>Fees",
                "Value": -total_capital_call_fees,
                "Running_Total": current_running_total_wf,
                "Type": "fee"
            })
        
        # Add capital call priority return if applicable
        if include_capital_calls and total_capital_call_pr > 0:
            current_running_total_wf -= total_capital_call_pr
            waterfall_data_wf.append({
                "Step": "Capital Call<br>PR",
                "Value": -total_capital_call_pr,
                "Running_Total": current_running_total_wf,
                "Type": "fee"
            })
        
        # Add fund-level priority return (PR Fund)
        fund_pr = carry_calculation.get('hurdle_return', 0)
        if fund_pr > 0:
            current_running_total_wf -= fund_pr
            waterfall_data_wf.append({
                "Step": "PR (Fund)",
                "Value": -fund_pr,
                "Running_Total": current_running_total_wf,
                "Type": "fee"
            })
        
        # Calculate GP catchup and carry (following original logic)
        total_exit_value = sum(item.get("Exit Value", 0) for item in fee_tracker)
        distributable_value = total_exit_value - total_mgmt_fees
        total_calls = sum(company_net_investments.values()) + total_capital_call_fees
        total_priority_return = fund_pr + total_capital_call_pr
        
        remaining_after_calls_and_pr = distributable_value - total_calls - total_priority_return
        gp_catchup_formula_amount = (total_priority_return / 0.8) * 0.2
        gp_catchup_new_formula = max(0, min(remaining_after_calls_and_pr, gp_catchup_formula_amount))
        
        if gp_catchup_new_formula > 0:
            current_running_total_wf -= gp_catchup_new_formula
            waterfall_data_wf.append({
                "Step": "GP Catchup",
                "Value": -gp_catchup_new_formula,
                "Running_Total": current_running_total_wf,
                "Type": "fee"
            })
        
        # Calculate GP carry
        remaining_for_carry = max(0, remaining_after_calls_and_pr - gp_catchup_new_formula)
        gp_carry_new_formula = remaining_for_carry * 0.20
        
        if gp_carry_new_formula > 0:
            current_running_total_wf -= gp_carry_new_formula
            waterfall_data_wf.append({
                "Step": "GP Carry (20%)",
                "Value": -gp_carry_new_formula,
                "Running_Total": current_running_total_wf,
                "Type": "fee"
            })
        
        # Final value
        waterfall_data_wf.append({
            "Step": "Final<br>Fund Value",
            "Value": 0,
            "Running_Total": current_running_total_wf,
            "Type": "final"
        })
        
        # Create the chart
        x_labels = [item["Step"] for item in waterfall_data_wf]
        y_values = [item["Value"] for item in waterfall_data_wf]
        text_labels = [f"${abs(item['Value']):,.0f}" if item['Value'] != 0 else f"${item['Running_Total']:,.0f}" for item in waterfall_data_wf]
        
        # Set measures for waterfall
        measures = []
        for item in waterfall_data_wf:
            if item["Type"] == "initial":
                measures.append("absolute")
            elif item["Type"] == "final":
                measures.append("total")
            else:
                measures.append("relative")
        
        fig = go.Figure(go.Waterfall(
            name="Enhanced Waterfall with IPO",
            orientation="v",
            measure=measures,
            x=x_labels,
            y=y_values,
            text=text_labels,
            textposition="outside",
            connector={"line": {"color": "rgb(63, 63, 63)"}},
            increasing={"marker": {"color": "#28B463"}},
            decreasing={"marker": {"color": "#E74C3C"}},
            totals={"marker": {"color": "#8E44AD"}}
        ))
        
        fig.update_layout(
            title="Traditional Waterfall Enhanced with IPO Distributions",
            xaxis_title="Waterfall Steps",
            yaxis_title="Value ($)",
            height=1100,  # Increased height for annotations
            showlegend=False,
            yaxis=dict(tickformat='$,.0f'),
            xaxis=dict(tickangle=-45),
            margin=dict(b=150, t=300, l=80, r=80)  # Increased top margin
        )
        
        # Calculate y-axis range for annotations
        max_y = max([item["Running_Total"] for item in waterfall_data_wf])
        y_range_padding = max_y * 0.4
        y_axis_max = max_y + y_range_padding
        fig.update_layout(yaxis=dict(range=[0, y_axis_max]))
        
        # Add annotations for company details and capital recovery
        capital_recovery_shown = False
        
        # Build company metrics for annotations
        company_metrics = {}
        for item in fee_tracker:
            if item["Status"] == "Active":
                company = item["Company"]
                net_investment = company_net_investments.get(company, 0)
                exit_value = item.get("Exit Value", 0)
                gross_moic = exit_value / net_investment if net_investment > 0 else 0
                
                # Calculate IRR (simplified)
                exit_date = pd.to_datetime(item["Exit Date"])
                investment_date = pd.to_datetime("2021-01-01")  # Default investment date
                years_held = (exit_date - investment_date).days / 365.25
                gross_irr = ((gross_moic ** (1/years_held)) - 1) * 100 if years_held > 0 and gross_moic > 0 else None
                
                company_metrics[company] = {
                    'gross_moic': gross_moic,
                    'gross_irr': gross_irr
                }
        
        # Add annotations for each waterfall step
        for i, item in enumerate(waterfall_data_wf):
            # Company exit annotations
            if item.get("Type") == "exit" and "Company" in item:
                company = item["Company"]
                if company in company_metrics:
                    metrics = company_metrics[company]
                    annotation_text = f"<b>{company}</b><br>MOIC: {metrics['gross_moic']:.2f}x<br>"
                    annotation_text += f"IRR: {metrics['gross_irr']:.1f}%" if metrics['gross_irr'] is not None else "IRR: N/A"
                    
                    y_paper_coord = item["Running_Total"] / y_axis_max if y_axis_max > 0 else 0
                    fig.add_annotation(
                        x=i, y=y_paper_coord, yref='paper', 
                        text=annotation_text, 
                        showarrow=True, arrowhead=2, arrowcolor="black", arrowwidth=1, 
                        ax=0, ay=-80,
                        font=dict(size=9, color="black"), 
                        bgcolor="rgba(255, 255, 255, 0.8)",
                        bordercolor="black", borderwidth=1, opacity=0.9,
                        xanchor="center", yanchor="top"
                    )
            
            # IPO distribution annotations
            elif item.get("Type") == "ipo_distribution":
                company = item.get("Company", "")
                date_str = item.get("Date", "").strftime('%m/%d/%Y') if item.get("Date") else ""
                annotation_text = f"<b>{company}</b><br>IPO Distribution<br>{date_str}"
                
                y_paper_coord = item["Running_Total"] / y_axis_max if y_axis_max > 0 else 0
                fig.add_annotation(
                    x=i, y=y_paper_coord, yref='paper',
                    text=annotation_text,
                    showarrow=True, arrowhead=2, arrowcolor="blue", arrowwidth=1,
                    ax=0, ay=-80,
                    font=dict(size=9, color="blue"),
                    bgcolor="rgba(255, 255, 255, 0.8)",
                    bordercolor="blue", borderwidth=1, opacity=0.9,
                    xanchor="center", yanchor="top"
                )
            
            # Capital recovery annotation - show only when running total crosses zero (becomes positive)
            running_total = item["Running_Total"]

            # Check if this is the point where capital is fully recovered (running total becomes positive)
            if running_total > 0 and not capital_recovery_shown:
                # Check if previous running total was negative (to ensure this is the crossing point)
                if i == 0 or (i > 0 and waterfall_data_wf[i-1]["Running_Total"] <= 0):
                    recovery_text = "✅ Full Capital Recovered"
                    recovery_color = "green"
                    capital_recovery_shown = True

                    y_paper_coord = running_total / y_axis_max if y_axis_max > 0 else 0
                    fig.add_annotation(
                        x=i, y=y_paper_coord, yref='paper',
                        text=recovery_text,
                        showarrow=True, arrowhead=2, arrowcolor=recovery_color,
                        ax=80, ay=-160,
                        font=dict(size=12, color=recovery_color, family="Arial", weight="bold"),
                        bgcolor="rgba(255, 255, 255, 0.9)",
                        bordercolor=recovery_color, borderwidth=2,
                        xanchor="center", yanchor="top"
                    )
        
        # Add final MOIC annotation
        if waterfall_data_wf:
            final_total = waterfall_data_wf[-1]["Running_Total"]
            total_exit_value = sum(item.get("Exit Value", 0) for item in fee_tracker)
            fund_moic_val = total_exit_value / total_investment_wf if total_investment_wf > 0 else 0
            
            y_paper_coord = final_total / y_axis_max if y_axis_max > 0 else 0
            fig.add_annotation(
                x=len(waterfall_data_wf)-1, y=y_paper_coord, yref='paper', 
                text=f'MOIC: {fund_moic_val:.2f}x',
                showarrow=True, arrowhead=1, ax=0, ay=-40, 
                font=dict(size=12, color="black"),
                bgcolor="yellow", opacity=0.7
            )
        
        st.plotly_chart(fig, use_container_width=True, key="enhanced_waterfall_with_ipo")
        

    except Exception as e:
        st.error(f"Error creating enhanced waterfall with IPO: {str(e)}")
        print(f"Enhanced waterfall error: {traceback.format_exc()}")









