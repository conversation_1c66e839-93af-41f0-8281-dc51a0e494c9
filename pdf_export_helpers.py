from __future__ import annotations

"""pdf_export_helpers.py
Utility functions to convert dataframes + simple HTML into a PDF for download inside Streamlit.
The main entry point is ``generate_pdf`` which returns PDF bytes ready for st.download_button().

Two conversion engines are tried, in order: ``pdfkit`` (wkhtmltopdf) and ``weasyprint``.  No disk
I/O is performed – the PDF is created fully in-memory.
"""

import os
import shutil
from typing import Dict, Any, List

# third-party
import pandas as pd


# -----------------------------------------------------------------------------
# Internal helpers
# -----------------------------------------------------------------------------

def _html_to_pdf_bytes(html: str) -> bytes:
    """Convert *html* string to PDF bytes.

    1. Try **pdfkit.from_string** with `to_file=False` which returns bytes (requires wkhtmltopdf).
    2. Fall back to **weasyprint.HTML(...).write_pdf()** which also returns bytes.

    Raises the last caught exception if neither backend is available.
    """
    last_err: Exception | None = None

    # Attempt WeasyPrint first
    try:
        from weasyprint import HTML  # type: ignore
        return HTML(string=html).write_pdf()
    except Exception as exc:
        last_err = exc  # Keep for potential fallback or final raise

    # Fallback to pdfkit – ensure wkhtmltopdf binary is locatable
    try:
        import pdfkit  # type: ignore

        # --- locate wkhtmltopdf executable ---------------------------------
        wkhtml_path: str | None = os.getenv("WKHTMLTOPDF_PATH")
        if not wkhtml_path:
            # Search in PATH or typical Windows install dir
            wkhtml_path = shutil.which("wkhtmltopdf") or \
                r"C:\\Program Files\\wkhtmltopdf\\bin\\wkhtmltopdf.exe"
            if not os.path.exists(wkhtml_path):
                wkhtml_path = None
        
        config = pdfkit.configuration(wkhtmltopdf=wkhtml_path) if wkhtml_path else None

        options = {
            "enable-local-file-access": None,
            "quiet": "",
            "page-size": "Letter",
            "margin-top": "0.5in",
            "margin-right": "0.5in",
            "margin-bottom": "0.5in",
            "margin-left": "0.5in",
            "orientation": "Portrait",
            "no-outline": None,
            "print-media-type": None,
            "dpi": 300,
            "viewport-size": "1200x1600"
        }

        return pdfkit.from_string(html, False, options=options, configuration=config)
    except Exception as exc:
        # If pdfkit also fails, and WeasyPrint failed before, update last_err
        # This ensures the error from the second attempt is available if both fail.
        last_err = exc

    # If we reach here, nothing worked
    raise RuntimeError("PDF generation failed – install WeasyPrint or pdfkit (wkhtmltopdf)") from last_err


def _df_to_html(title: str, df: pd.DataFrame | None) -> str:
    """Render *df* as an HTML table preceded by a heading. Returns empty string if df is None/empty."""
    if df is None or df.empty:
        return ""

    # Format numeric columns with commas
    df_formatted = df.copy()
    for col in df_formatted.columns:
        if 'Net Investment' in col or 'Book Value' in col:
            # Check if column contains numeric data
            if pd.api.types.is_numeric_dtype(df_formatted[col]):
                df_formatted[col] = df_formatted[col].apply(lambda x: f"{x:,.0f}" if pd.notna(x) else "")
    
    # Use pandas built-in HTML, no index.
    table_html = df_formatted.to_html(index=False, border=0, classes="dataframe")
    return f"<h2>{title}</h2>\n{table_html}"


# -----------------------------------------------------------------------------
# Public API
# -----------------------------------------------------------------------------

def generate_pdf(
    edited_df: pd.DataFrame,
    summary_metrics: Dict[str, Any] | None = None,
    chart_data: str | None = None, # Renamed from waterfall_b64
    chart_type: str = "png",      # New parameter with default
    logo_b64: str | None = None,
) -> bytes:
    """Return PDF bytes containing *edited_df* (exit plan table) and optional *summary_metrics*.

    Parameters
    ----------
    edited_df:
        DataFrame representing the exit plan (company, MOIC, exit date, etc.).
    summary_metrics:
        Optional mapping of metric name → value (numbers already formatted as strings) to
        be shown in a second table.
    chart_data:
        Optional base-64 encoded image data of the waterfall chart.
    chart_type:
        The type of the chart image ('png' or 'svg'). Defaults to 'png'.
    logo_b64:
        Optional base-64 encoded logo to display at the top-left of the PDF.

    Returns
    -------
    bytes
        Binary PDF contents ready to pass into Streamlit ``st.download_button`` as *data*.
    """
    html_parts: List[str] = [
        "<html><head><meta charset='utf-8'>",
        """<style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        body {
            font-family: 'Inter', sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 20px;
            background: #f5f7fa;
            color: #2d3748;
            width: 100%;
            max-width: 100%;
        }

        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding-bottom: 20px;
            border-bottom: 1px solid #e1e7f0;
            margin-bottom: 20px;
        }

        .logo {
            height: 50px;
            width: auto;
        }
        
        h1 {
            text-align: center;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 0;
            color: #2d3748;
            flex-grow: 1;
        }
        
        h2 {
            color: #2d3748;
            font-weight: 600;
            font-size: 20px;
            margin-top: 32px;
            margin-bottom: 16px;
        }
        
        .section-container {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
            border: 1px solid rgba(102, 126, 234, 0.1);
            width: 100%;
        }
        
        table {
            border-collapse: collapse;
            width: 100%;
            max-width: 100%;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            table-layout: auto;
            word-wrap: break-word;
        }
        
        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 600;
            padding: 8px 12px;
            text-align: left;
            border: none;
            font-size: 11px;
            white-space: normal;
            word-break: break-word;
        }
        
        td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #e1e7f0;
            font-size: 11px;
            white-space: normal;
            word-break: break-word;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        tr:nth-child(even) {
            background-color: #f8f9ff;
        }
        
        tr:hover {
            background-color: #e8edff;
        }
        
        .metrics-container {
            display: flex;
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .metric-card {
            flex: 1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            color: white;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.25);
        }
        
        .metric-label {
            font-size: 11px;
            font-weight: 500;
            opacity: 0.9;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 8px;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: 700;
        }
        
        .waterfall-container {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
            border: 1px solid rgba(102, 126, 234, 0.1);
            page-break-inside: avoid;
            overflow: visible;
        }
        
        .waterfall-container img {
            display: block;
            margin: 0 auto;
            width: 100%;
            max-width: 100%;
            height: auto;
            max-height: none;
            object-fit: contain;
        }
        
        @media print {
            body {
                background: white;
                padding: 20px;
            }
            .waterfall-container {
                page-break-inside: avoid !important;
            }
            .section-container, .waterfall-container {
                box-shadow: none;
                border: 1px solid #e1e7f0;
            }
        }
        </style></head><body>""",
        '<div class="header-container">',
        f'<img src="data:image/jpeg;base64,{logo_b64}" class="logo">' if logo_b64 else '',
        "<h1>Fund Exit IRR Modeler</h1>",
        '</div>',
        '<div class="section-container">',
        _df_to_html("Exit Plan", edited_df),
        '</div>',
    ]

    # Add summary metrics before waterfall chart
    if summary_metrics:
        html_parts.append('<div class="metrics-container">')
        for metric_name, metric_value in summary_metrics.items():
            html_parts.append(f'''
                <div class="metric-card">
                    <div class="metric-label">{metric_name}</div>
                    <div class="metric-value">{metric_value}</div>
                </div>
            ''')
        html_parts.append('</div>')

    # Optionally embed waterfall chart image
    if chart_data:
        html_parts.append('<div class="waterfall-container">')
        html_parts.append("<h2>Fund Exit Value Waterfall</h2>")
        if chart_type == "svg":
            img_src = f"data:image/svg+xml;base64,{chart_data}"
        else: # Default to png
            img_src = f"data:image/png;base64,{chart_data}"
        html_parts.append(f"<img src='{img_src}' />")
        html_parts.append('</div>')

    html_parts.append("</body></html>")
    full_html = "".join(html_parts)

    return _html_to_pdf_bytes(full_html)