FROM python:3.9-slim

WORKDIR /app

# Install system dependencies (including for PDF generation, curl, and Google Chrome)
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    curl \
    wget \
    gnupg \
    # Dependencies for WeasyPrint & wkhtmltopdf (some overlap)
    libpango-1.0-0 \
    libcairo2 \
    libgdk-pixbuf2.0-0 \
    libffi-dev \
    shared-mime-info \
    fontconfig \
    libfontconfig1 \
    libxrender1 \
    # For pdfkit (wkhtmltopdf)
    wkhtmltopdf \
    # For running wkhtmltopdf headlessly
    xvfb \
    # Dependencies for Chrome/Chromium
    libnss3 \
    libatk-bridge2.0-0 \
    libdrm2 \
    libxcomposite1 \
    libxdamage1 \
    libxrandr2 \
    libgbm1 \
    libxss1 \
    libasound2 && \
    # Install Google Chrome
    curl -sSL https://dl.google.com/linux/linux_signing_key.pub | apt-key add - && \
    echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google-chrome.list && \
    apt-get update && \
    apt-get install -y google-chrome-stable && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir --upgrade streamlit>=1.42.0 authlib>=1.3.2

# Copy application code
COPY . .

# Create a placeholder directory for mounting key file
RUN mkdir -p /app/keys

# Set Docker environment variable
ENV DOCKER_ENV=true

# Expose port for Streamlit
EXPOSE 8502

# Health check
HEALTHCHECK CMD curl --fail http://localhost:8502/_stcore/health || exit 1

# Run the application directly
ENTRYPOINT ["streamlit", "run", "app.py", "--server.port=8502", "--server.address=0.0.0.0"]