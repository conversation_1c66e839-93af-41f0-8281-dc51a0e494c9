# ipo_scenario.py
# IPO Scenario module for handling distribution in-kind over 3 years

import streamlit as st
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from ipo_scenario_advanced import (
    calculate_ipo_distributions_advanced,
    display_custom_percentage_editor,
    apply_fees_to_ipo_distributions,
    create_ipo_visualization,
    validate_custom_percentages
)
from ipo_scenario_phase2 import (
    display_ipo_scenario_section_phase2,
    create_ipo_distribution_df_advanced
)

# For backward compatibility, import Phase 2 function as the main function
display_ipo_scenario_section = display_ipo_scenario_section_phase2
create_ipo_distribution_df = create_ipo_distribution_df_advanced

def calculate_ipo_distributions(company_name, exit_value, exit_date, distribution_years=3, distribution_pattern='equal', first_sale_date=None):
    """
    Legacy function for backward compatibility. 
    Calls the advanced version with default parameters.
    """
    return calculate_ipo_distributions_advanced(
        company_name=company_name,
        exit_value=exit_value,
        exit_date=exit_date,
        first_sale_date=first_sale_date,
        distribution_years=distribution_years,
        distribution_pattern=distribution_pattern,
        custom_percentages=None,
        distribution_frequency='semi-annual'
    )

def apply_ipo_to_cashflows(fund_flows, ipo_schedules, fee_tracker, company_net_investments):
    """
    Apply IPO distributions to fund cash flows.
    
    Args:
        fund_flows: List of (date, amount) tuples
        ipo_schedules: List of IPO distribution schedules
        fee_tracker: List of fee tracking dictionaries
        company_net_investments: Dictionary of company net investments
    
    Returns:
        Modified fund_flows
    """
    if not ipo_schedules:
        return fund_flows
    
    # Create a mapping of company names to their IPO schedules
    ipo_company_map = {s['company_name']: s for s in ipo_schedules}
    
    # Create a set of IPO company names for faster lookup
    ipo_companies = set(ipo_company_map.keys())
    
    # Create a mapping of IPO companies to their exit values for matching
    ipo_exit_values = {}
    for schedule in ipo_schedules:
        company_name = schedule['company_name']
        exit_value = schedule['exit_value']
        ipo_exit_values[company_name] = exit_value
    
    # Remove the original exit cash flows for IPO companies
    # and replace with distribution cash flows
    modified_flows = []
    
    # Debug: Track what's being processed
    debug_mode = False  # Set to True for debugging
    if debug_mode:
        print(f"DEBUG: Processing {len(fund_flows)} fund flows")
        print(f"DEBUG: IPO companies: {ipo_companies}")
        print(f"DEBUG: IPO exit values: {ipo_exit_values}")
    
    for date, amount in fund_flows:
        # Check if this cash flow is from an IPO company exit
        is_ipo_exit = False
        flow_date = pd.to_datetime(date)
        
        if debug_mode and amount > 0:  # Only debug positive flows
            print(f"DEBUG: Checking flow {flow_date.strftime('%Y-%m-%d')}: ${amount:,.0f}")
        
        # Method 1: Check using fee_tracker matching
        for fee_entry in fee_tracker:
            if fee_entry['Company'] in ipo_companies:
                # Convert dates to ensure compatibility
                fee_exit_date = pd.to_datetime(fee_entry['Exit Date'])
                
                # Check if dates match (within 1 day to handle any date format issues)
                if abs((fee_exit_date - flow_date).days) <= 1:
                    # This is an IPO company exit - skip it
                    is_ipo_exit = True
                    if debug_mode:
                        print(f"DEBUG: Method 1 - Matched IPO exit for {fee_entry['Company']}")
                    break
        
        # Method 2: Check using value matching for IPO companies (more robust)
        if not is_ipo_exit and amount > 0:  # Only check positive flows (exits)
            for company_name, expected_exit_value in ipo_exit_values.items():
                # Check if the exit value approximately matches (within 1% or $1000, whichever is larger)
                tolerance = max(1000, expected_exit_value * 0.01)
                if abs(amount - expected_exit_value) < tolerance:
                    is_ipo_exit = True
                    if debug_mode:
                        print(f"DEBUG: Method 2 - Matched IPO exit for {company_name} by value")
                    break
        
        # Method 3: Check if this is a large positive flow that could be an IPO company exit
        if not is_ipo_exit and amount > 0:
            # Check if this amount matches any of the expected IPO exit values
            for company_name in ipo_companies:
                net_investment = company_net_investments.get(company_name, 0)
                if net_investment > 0:
                    # Calculate expected exit value from net investment
                    # If the amount is much larger than the net investment, it could be an exit
                    if amount > net_investment * 1.5:  # At least 1.5x return
                        # Check if there's a fee tracker entry for this company with similar timing
                        for fee_entry in fee_tracker:
                            if fee_entry['Company'] == company_name:
                                fee_exit_date = pd.to_datetime(fee_entry['Exit Date'])
                                # Check if the dates are close (within 30 days)
                                if abs((fee_exit_date - flow_date).days) <= 30:
                                    is_ipo_exit = True
                                    if debug_mode:
                                        print(f"DEBUG: Method 3 - Matched IPO exit for {company_name} by timing")
                                    break
                        if is_ipo_exit:
                            break
        
        if not is_ipo_exit:
            modified_flows.append((date, amount))
        elif debug_mode:
            print(f"DEBUG: Skipping IPO exit flow: {flow_date.strftime('%Y-%m-%d')}: ${amount:,.0f}")
    
    # Add IPO distribution cash flows
    for schedule in ipo_schedules:
        for dist_date, dist_amount in zip(schedule['distribution_dates'], schedule['distribution_amounts']):
            modified_flows.append((dist_date, dist_amount))
    
    return modified_flows

def calculate_ipo_metrics(original_irr, ipo_irr, original_moic, ipo_moic):
    """
    Calculate comparison metrics between standard exit and IPO scenarios.
    
    Returns:
        Dictionary with comparison metrics
    """
    return {
        'irr_difference': (ipo_irr - original_irr) * 100 if original_irr and ipo_irr else None,
        'moic_difference': ipo_moic - original_moic if original_moic and ipo_moic else None,
        'irr_relative_change': ((ipo_irr / original_irr) - 1) * 100 if original_irr and ipo_irr and original_irr != 0 else None
    }
