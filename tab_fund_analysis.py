# tab_fund_analysis_ipo.py
# Enhanced fund analysis tab with IPO scenario support

import streamlit as st
import pandas as pd
import numpy as np

from financial_calculations import (
    xirr, 
    aggregate_cashflows_by_date, 
    calculate_fund_moic,
    calculate_gp_carry,
    clear_capital_call_cache
)
from excel_export_helpers import get_fund_analysis_data
from fund_analysis_helpers import (
    prepare_company_data,
    create_plan_df,
    display_and_process_plan_editor,
    process_exits_and_fees,
)
from fund_analysis_waterfall import display_waterfall_chart
from fund_analysis_details_ui import display_details_section, calculate_gross_irr, calculate_net_irr, calculate_gross_moic, calculate_net_moic
from capital_recovery_analysis import (
    display_capital_recovery_warnings,
    display_capital_recovery_analysis
)
from ipo_scenario_radio_ui import (
    display_ipo_scenario_section_radio_ui as display_ipo_scenario_section,
)
from ipo_scenario import (
    apply_ipo_to_cashflows,
    calculate_ipo_metrics
)
from ipo_scenario_dynamic import (
    apply_ipo_to_cashflows_dynamic,
    calculate_dynamic_fees_for_ipo
)


def display_fund_analysis_tab(data_df, company_list, fee_balance, fee_date, q_fee, target_irr_val, company_fees, fee_mapping, gp_commitment=0, call_dates=None, investment_pr_amounts=None):
    clear_capital_call_cache()
    
    include_capital_calls = True
    
    latest_date = data_df["Date"].max()
    default_exit_date = (pd.to_datetime(latest_date) + pd.DateOffset(months=12)).date()

    # Prepare company data
    company_net_investments, company_book_values, company_moics, company_has_flows, written_off_companies = prepare_company_data(data_df, company_list, investment_pr_amounts)
    
    # Ensure we have valid data before proceeding
    if not company_net_investments:
        st.error("No company investment data available. Please check your data source.")
        return
    
    # Create and display plan editor with session state management
    plan_df = create_plan_df(company_list, company_net_investments, company_book_values, company_moics, company_has_flows, written_off_companies, default_exit_date)
    edited_df = display_and_process_plan_editor(plan_df, written_off_companies)
    
    # Add exit values to edited_df for IPO scenario
    if 'Exit Value' not in edited_df.columns:
        # Use Net Investment × MOIC, not Book Value × MOIC
        edited_df['Exit Value'] = edited_df.apply(
            lambda row: company_net_investments.get(row['Company'], 0) * row['MOIC'], 
            axis=1
        )
    
    # Initialize variables that will be used later
    hurdle_breakdown_df = None
    fee_tracker = []
    exit_dates = {}
    exit_timeline = edited_df.copy()
    carry_calculation = {}
    ipo_schedules = None
    
    # Only proceed with expensive calculations if needed
    should_recalculate = st.session_state.get('should_recalculate', True) or st.session_state.get('ipo_scenario_changed', False)
    
    if should_recalculate:
        with st.spinner("Calculating fund analysis..."):
            # Store the edited dataframe
            st.session_state.fund_analysis_edited_df = edited_df

            # Get summary metrics from external calculation
            summary_metrics, _, _, _, _, _, _, hurdle_breakdown_df = get_fund_analysis_data(
                data_df, edited_df, fee_balance, fee_date, q_fee, target_irr_val, company_list, include_capital_calls, company_fees, fee_mapping, gp_commitment, call_dates
            )
            
            exit_timeline = edited_df.sort_values("Exit Date").reset_index(drop=True)
            exit_timeline['Exit Date'] = pd.to_datetime(exit_timeline['Exit Date'], errors='coerce')
            
            # Process exits and fees
            fund_flows, fee_tracker, exit_dates = process_exits_and_fees(
                data_df, exit_timeline, fee_balance, fee_date, q_fee, written_off_companies,
                company_net_investments, include_capital_calls, fee_mapping, company_fees, company_book_values,
                ipo_schedules=st.session_state.get('ipo_schedules')
            )
            
            # Store original fund flows for comparison
            original_fund_flows = fund_flows.copy()
            
            # IPO scenario will be handled after this calculation block
            # to avoid duplicate widget rendering
            
            # Note: IPO scenario will be applied during display phase
            
            # Add remaining companies at net investment value
            for company_rem in company_list:
                if not any(ft_item["Company"] == company_rem for ft_item in fee_tracker):
                    net_investment_rem = company_net_investments.get(company_rem, 0)
                    if net_investment_rem > 0:
                        comp_data_rem = data_df[data_df["Deal Name"] == company_rem]
                        if not comp_data_rem.empty:
                            latest_date_rem = comp_data_rem["Date"].max()
                            fund_flows.append((latest_date_rem, net_investment_rem))
                        else:
                            fund_flows.append((latest_date, net_investment_rem))
            
            # Final calculations
            fund_flows_agg = aggregate_cashflows_by_date(fund_flows)
            fund_irr_val = xirr(fund_flows_agg)
            
            # Calculate original metrics without IPO
            original_fund_flows_agg = aggregate_cashflows_by_date(original_fund_flows)
            original_fund_irr_val = xirr(original_fund_flows_agg)
            
            fund_moic_val, total_investment, total_value = calculate_fund_moic(data_df, fee_tracker, edited_df, gp_commitment, company_net_investments)
            carry_calculation = calculate_gp_carry(data_df, total_value, exit_timeline=exit_timeline, gp_commitment=gp_commitment, company_net_investments=company_net_investments, call_dates=call_dates)
            
            # Calculate Gross IRR and Net IRR
            gross_irr_val = calculate_gross_irr(ipo_schedules, edited_df, company_net_investments, data_df)
            net_irr_val = calculate_net_irr(ipo_schedules, edited_df, company_net_investments, data_df, fee_tracker, fee_balance, fee_date, q_fee)
            
            # Calculate Gross MOIC and Net MOIC
            gross_moic_val = calculate_gross_moic(ipo_schedules, edited_df, company_net_investments, data_df)
            net_moic_val = calculate_net_moic(ipo_schedules, edited_df, company_net_investments, data_df, fee_tracker, fee_balance, fee_date, q_fee)
            
            # Store results in session state to avoid recalculation
            st.session_state.fund_analysis_results = {
                'fund_irr_val': fund_irr_val,
                'fund_moic_val': fund_moic_val,
                'total_investment': total_investment,
                'total_value': total_value,
                'carry_calculation': carry_calculation,
                'fee_tracker': fee_tracker,
                'exit_dates': exit_dates,
                'fund_flows_agg': fund_flows_agg,
                'hurdle_breakdown_df': hurdle_breakdown_df,
                'exit_timeline': exit_timeline,
                'original_fund_irr_val': original_fund_irr_val,
                'ipo_schedules': ipo_schedules,
                'original_fund_flows': original_fund_flows,
                'ipo_fund_flows': fund_flows,
                'gross_irr_val': gross_irr_val,
                'net_irr_val': net_irr_val,
                'gross_moic_val': gross_moic_val,
                'net_moic_val': net_moic_val
            }
            
            # Mark as calculated
            st.session_state.should_recalculate = False
            st.session_state.ipo_scenario_changed = False
    else:
        # Use cached results
        if 'fund_analysis_results' in st.session_state:
            results = st.session_state.fund_analysis_results
            fund_irr_val = results['fund_irr_val']
            fund_moic_val = results['fund_moic_val']
            total_investment = results['total_investment']
            total_value = results['total_value']
            carry_calculation = results['carry_calculation']
            fee_tracker = results['fee_tracker']
            exit_dates = results['exit_dates']
            fund_flows_agg = results['fund_flows_agg']
            hurdle_breakdown_df = results['hurdle_breakdown_df']
            exit_timeline = results['exit_timeline']
            original_fund_irr_val = results.get('original_fund_irr_val', fund_irr_val)
            ipo_schedules = results.get('ipo_schedules', None)
            original_fund_flows = results.get('original_fund_flows', [])
            ipo_fund_flows = results.get('ipo_fund_flows', [])
            gross_irr_val = results.get('gross_irr_val', None)
            net_irr_val = results.get('net_irr_val', None)
            gross_moic_val = results.get('gross_moic_val', None)
            net_moic_val = results.get('net_moic_val', None)
        else:
            # Force recalculation if no cached results
            st.session_state.should_recalculate = True
            st.rerun()
            return
    
    # Display IPO scenario section (only once, after all calculations)
    st.divider()
    current_ipo_schedules = display_ipo_scenario_section(edited_df, company_net_investments, fee_tracker, sum(info.get('Total Fees', 0) for info in fee_tracker))
    
    # Apply IPO scenario if it's currently active and different from stored
    if current_ipo_schedules and current_ipo_schedules != ipo_schedules:
        # Apply IPO scenario to fund flows
        if 'fund_analysis_results' in st.session_state:
            results = st.session_state.fund_analysis_results
            original_fund_flows = results.get('original_fund_flows', [])

            # DYNAMIC FEE CALCULATION FOR IPO
            fund_flows, ipo_distribution_schedules = apply_ipo_to_cashflows_dynamic(
                original_fund_flows.copy(), current_ipo_schedules, fee_tracker, company_net_investments
            )
            
            fund_flows = calculate_dynamic_fees_for_ipo(
                fund_flows, ipo_distribution_schedules, fee_balance, fee_date, q_fee, company_fees, fee_mapping, company_net_investments
            )

            # Recalculate metrics with IPO applied
            fund_flows_agg = aggregate_cashflows_by_date(fund_flows)
            fund_irr_val = xirr(fund_flows_agg)
            
            # Recalculate Gross IRR and Net IRR with IPO
            gross_irr_val = calculate_gross_irr(current_ipo_schedules, edited_df, company_net_investments, data_df)
            net_irr_val = calculate_net_irr(current_ipo_schedules, edited_df, company_net_investments, data_df, fee_tracker, fee_balance, fee_date, q_fee)
            
            # Recalculate Gross MOIC and Net MOIC with IPO
            gross_moic_val = calculate_gross_moic(current_ipo_schedules, edited_df, company_net_investments, data_df)
            net_moic_val = calculate_net_moic(current_ipo_schedules, edited_df, company_net_investments, data_df, fee_tracker, fee_balance, fee_date, q_fee)
            
            # Update the results with new IPO data
            st.session_state.fund_analysis_results.update({
                'ipo_schedules': current_ipo_schedules,
                'ipo_fund_flows': fund_flows,
                'fund_irr_val': fund_irr_val,
                'fund_flows_agg': fund_flows_agg,
                'gross_irr_val': gross_irr_val,
                'net_irr_val': net_irr_val,
                'gross_moic_val': gross_moic_val,
                'net_moic_val': net_moic_val
            })
            
            # Update current variables
            ipo_schedules = current_ipo_schedules
    elif not current_ipo_schedules and ipo_schedules:
        # IPO scenario was disabled, revert to original calculations
        if 'fund_analysis_results' in st.session_state:
            results = st.session_state.fund_analysis_results
            original_fund_flows = results.get('original_fund_flows', [])
            
            # Use original flows without IPO
            fund_flows_agg = aggregate_cashflows_by_date(original_fund_flows)
            fund_irr_val = xirr(fund_flows_agg)
            
            # Recalculate Gross IRR and Net IRR without IPO
            gross_irr_val = calculate_gross_irr(None, edited_df, company_net_investments, data_df)
            net_irr_val = calculate_net_irr(None, edited_df, company_net_investments, data_df, fee_tracker, fee_balance, fee_date, q_fee)
            
            # Recalculate Gross MOIC and Net MOIC without IPO
            gross_moic_val = calculate_gross_moic(None, edited_df, company_net_investments, data_df)
            net_moic_val = calculate_net_moic(None, edited_df, company_net_investments, data_df, fee_tracker, fee_balance, fee_date, q_fee)
            
            # Update the results
            st.session_state.fund_analysis_results.update({
                'ipo_schedules': None,
                'ipo_fund_flows': original_fund_flows,
                'fund_irr_val': fund_irr_val,
                'fund_flows_agg': fund_flows_agg,
                'gross_irr_val': gross_irr_val,
                'net_irr_val': net_irr_val,
                'gross_moic_val': gross_moic_val,
                'net_moic_val': net_moic_val
            })
            
            # Update current variables
            ipo_schedules = None
    
    # Display metrics
    if fund_irr_val is not None:
        # Standard metrics display
        metric_col1, metric_col2, metric_col3, metric_col4, metric_col5 = st.columns(5)
        with metric_col1:
            if gross_moic_val is not None:
                st.metric("Gross MOIC", f"{gross_moic_val:.2f}x")
            else:
                st.metric("Gross MOIC", "N/A")
        
        with metric_col2:
            if net_moic_val is not None:
                st.metric("Net MOIC", f"{net_moic_val:.2f}x")
            else:
                st.metric("Net MOIC", "N/A")
            
        with metric_col3:
            if gross_irr_val is not None:
                st.metric("Gross IRR", f"{gross_irr_val * 100:.2f}%")
            else:
                st.metric("Gross IRR", "N/A")
                
        with metric_col4:
            if net_irr_val is not None:
                st.metric("Net IRR", f"{net_irr_val * 100:.2f}%")
            else:
                st.metric("Net IRR", "N/A")
            
        with metric_col5:
            total_exit_value_top = sum(exit_info_item.get("Exit Value", 0) for exit_info_item in fee_tracker)
            total_mgmt_fees_top = sum(exit_info_item.get("Management Fees", 0) for exit_info_item in fee_tracker)
            distributable_value_top = total_exit_value_top - total_mgmt_fees_top
            
            total_capital_call_fees_top = sum(exit_info_item.get("Capital Call Fees", 0) for exit_info_item in fee_tracker)
            total_calls_top = sum(company_net_investments.values()) + total_capital_call_fees_top
            
            total_capital_call_pr_top = sum(exit_info_item.get("Capital Call PR", 0) for exit_info_item in fee_tracker)
            total_priority_return_top = carry_calculation['hurdle_return'] + total_capital_call_pr_top
            
            remaining_after_calls_and_pr_top = distributable_value_top - total_calls_top - total_priority_return_top
            
            gp_catchup_formula_amount_top = (total_priority_return_top / 0.8) * 0.2
            
            gp_catchup_top = min(remaining_after_calls_and_pr_top, gp_catchup_formula_amount_top)
            gp_catchup_top = max(0, gp_catchup_top)
            
            remaining_for_carry_top = max(0, remaining_after_calls_and_pr_top - gp_catchup_top)
            gp_carry_top = remaining_for_carry_top * 0.20
            
            total_gp_compensation_display = gp_catchup_top + gp_carry_top

            # Persist metrics for later PDF export
            st.session_state.fund_summary_metrics = {
                "Gross MOIC (x)": f"{gross_moic_val:.2f}" if gross_moic_val is not None else "N/A",
                "Net MOIC (x)": f"{net_moic_val:.2f}" if net_moic_val is not None else "N/A",
                "Gross IRR (%)": f"{gross_irr_val * 100:.2f}" if gross_irr_val is not None else "N/A",
                "Net IRR (%)": f"{net_irr_val * 100:.2f}" if net_irr_val is not None else "N/A",
                "Total GP Compensation ($)": f"{total_gp_compensation_display:,.0f}",
                "IPO Scenario Active": bool(current_ipo_schedules)
            }

            st.metric("Total GP Compensation", f"${total_gp_compensation_display:,.0f}")
    

    
    # Display capital recovery warnings after metrics
    try:
        if not edited_df.empty and company_net_investments:
            if st.checkbox("", value=True, key="show_recovery_warnings"):
                display_capital_recovery_warnings(edited_df, company_net_investments)
    except Exception as e:
        st.error(f"Error in capital recovery warnings: {str(e)}")
    
    # Display waterfall chart with IPO schedules
    display_waterfall_chart(fee_tracker, company_net_investments, data_df, total_value, carry_calculation, include_capital_calls, gp_commitment, edited_df, current_ipo_schedules)
    
    # Display capital recovery analysis
    written_off_companies_list = edited_df[edited_df["MOIC"] == 0]["Company"].tolist()
    if written_off_companies_list or st.checkbox("Show Capital Recovery Analysis", value=bool(written_off_companies_list)):
        st.divider()
        display_capital_recovery_analysis(fee_tracker, company_net_investments, written_off_companies_list, current_ipo_schedules)
    
    # Display details section
    display_details_section(
        fee_tracker, exit_dates, fee_balance, fee_date, q_fee, company_fees, fee_mapping, 
        include_capital_calls, exit_timeline, company_net_investments, company_book_values,
        carry_calculation, hurdle_breakdown_df, current_ipo_schedules, edited_df, data_df
    )
