# capital_recovery_analysis.py
# Capital Recovery Analysis for tracking written-off companies and fund return coverage

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import datetime

def track_capital_recovery(fee_tracker, company_net_investments, written_off_companies):
    """
    Track whether exit proceeds are sufficient to cover return of capital
    including written-off companies like Coronis.
    
    Args:
        fee_tracker: List of exit information dictionaries
        company_net_investments: Dictionary of company names to net investment amounts
        written_off_companies: List of company names that are written off
        
    Returns:
        Dictionary with capital recovery metrics
    """
    try:
        # Calculate total capital invested - ensure all values are numeric
        total_capital_invested = sum(float(v) if v is not None else 0 for v in company_net_investments.values())
        
        # Calculate capital lost from written-off companies
        written_off_capital = 0
        written_off_details = {}
        
        for company in written_off_companies:
            if company in company_net_investments:
                investment = float(company_net_investments[company]) if company_net_investments[company] is not None else 0
                written_off_capital += investment
                written_off_details[company] = investment
        
        # Calculate cumulative exit proceeds
        cumulative_exit_value = 0
        cumulative_fees = 0
        recovery_timeline = []
        
        # Sort exits by date
        sorted_exits = sorted(fee_tracker, key=lambda x: x["Exit Date"])
        
        for exit_info in sorted_exits:
            if exit_info["Status"] != "Written Off (Investment Deducted)":
                # Convert numpy types to regular Python types to avoid arithmetic issues
                exit_value = float(exit_info.get("Exit Value", 0)) if exit_info.get("Exit Value") is not None else 0
                total_fees = float(exit_info.get("Total Fees", 0)) if exit_info.get("Total Fees") is not None else 0
                
                cumulative_exit_value += exit_value
                cumulative_fees += total_fees
                
                net_proceeds = cumulative_exit_value - cumulative_fees
                capital_recovered = min(net_proceeds, total_capital_invested)  # Cap at total investment like waterfall chart
                capital_shortfall = total_capital_invested - capital_recovered
                written_off_coverage = max(0, capital_recovered - (total_capital_invested - written_off_capital))
                
                # Ensure exit date is properly formatted to avoid timestamp arithmetic issues
                exit_date = exit_info["Exit Date"]
                if hasattr(exit_date, 'date'):
                    exit_date = exit_date.date()
                elif pd.isna(exit_date):
                    exit_date = datetime.date.today()
                
                recovery_timeline.append({
                    "Exit Date": exit_date,
                    "Company": exit_info["Company"],
                    "Exit Value": float(exit_value),
                    "Cumulative Exit Value": float(cumulative_exit_value),
                    "Cumulative Fees": float(cumulative_fees),
                    "Net Proceeds": float(net_proceeds),
                    "Capital Recovered": float(capital_recovered),
                    "Capital Shortfall": float(capital_shortfall),
                    "Written Off Coverage": float(written_off_coverage),
                    "Written Off Shortfall": float(max(0, written_off_capital - written_off_coverage)),
                    "Recovery Percentage": float((capital_recovered / total_capital_invested * 100) if total_capital_invested > 0 else 0)
                })
        
        # Final metrics calculation
        final_net_proceeds = cumulative_exit_value - cumulative_fees
        final_capital_recovered = min(final_net_proceeds, total_capital_invested)  # Cap at total investment like waterfall chart
        final_capital_shortfall = total_capital_invested - final_capital_recovered
        final_written_off_coverage = max(0, final_capital_recovered - (total_capital_invested - written_off_capital))
        final_written_off_shortfall = max(0, written_off_capital - final_written_off_coverage)
        
        return {
            "total_capital_invested": float(total_capital_invested),
            "written_off_capital": float(written_off_capital),
            "written_off_details": {k: float(v) for k, v in written_off_details.items()},
            "cumulative_exit_value": float(cumulative_exit_value),
            "cumulative_fees": float(cumulative_fees),
            "final_net_proceeds": float(final_net_proceeds),
            "final_capital_recovered": float(final_capital_recovered),
            "final_capital_shortfall": float(final_capital_shortfall),
            "final_written_off_coverage": float(final_written_off_coverage),
            "final_written_off_shortfall": float(final_written_off_shortfall),
            "recovery_percentage": float((final_capital_recovered / total_capital_invested * 100) if total_capital_invested > 0 else 0),
            "written_off_recovery_percentage": float((final_written_off_coverage / written_off_capital * 100) if written_off_capital > 0 else 0),
            "recovery_timeline": recovery_timeline
        }
            
    except Exception as e:
        st.error(f"Error in track_capital_recovery: {str(e)}")
        return {
            "total_capital_invested": 0,
            "written_off_capital": 0,
            "written_off_details": {},
            "cumulative_exit_value": 0,
            "cumulative_fees": 0,
            "final_net_proceeds": 0,
            "final_capital_recovered": 0,
            "final_capital_shortfall": 0,
            "final_written_off_coverage": 0,
            "final_written_off_shortfall": 0,
            "recovery_percentage": 0,
            "written_off_recovery_percentage": 0,
            "recovery_timeline": []
        }


def check_capital_recovery_warnings(edited_df, company_net_investments):
    """
    Check for potential capital recovery issues and provide warnings.
    
    Args:
        edited_df: DataFrame with company exit plans including MOIC
        company_net_investments: Dictionary of company investments
        
    Returns:
        Dictionary with warning information
    """
    warnings = {
        "has_warnings": False,
        "critical_warnings": [],
        "moderate_warnings": [],
        "info_messages": []
    }
    
    try:
        # Calculate projected returns - ensure all values are numeric
        total_investment = sum(float(v) if v is not None else 0 for v in company_net_investments.values())
        if total_investment == 0:
            return warnings  # No investments, no warnings needed
            
        projected_exit_value = 0
        written_off_capital = 0
        
        company_returns = []
        
        for _, row in edited_df.iterrows():
            company = row["Company"]
            moic = float(row.get("MOIC", 0))  # Ensure it's a float
            investment = float(company_net_investments.get(company, 0))  # Ensure it's a float
            exit_value = investment * moic
            
            projected_exit_value += exit_value
            
            if moic == 0:
                written_off_capital += investment
            
            company_returns.append({
                "Company": company,
                "Investment": investment,
                "MOIC": moic,
                "Exit Value": exit_value,
                "Return": exit_value - investment
            })
        
        # Calculate key metrics
        gross_return = projected_exit_value - total_investment
        gross_return_percentage = (gross_return / total_investment * 100) if total_investment > 0 else 0
        
        # Estimate fees (simplified - you may want to use actual fee calculation)
        estimated_mgmt_fees = total_investment * 0.02 * 3  # Rough estimate: 2% for 3 years
        estimated_total_fees = estimated_mgmt_fees * 1.08  # Add hurdle
        
        net_return = gross_return - estimated_total_fees
        capital_coverage = projected_exit_value / total_investment if total_investment > 0 else 0
        
        # Check for critical warnings
        if capital_coverage < 1.0:
            warnings["has_warnings"] = True
            shortfall = total_investment - projected_exit_value
            warnings["critical_warnings"].append({
                "type": "CAPITAL_AT_RISK",
                "message": f"⚠️ Critical: Projected exits (${projected_exit_value:,.0f}) will not return full capital invested (${total_investment:,.0f}). Shortfall: ${shortfall:,.0f}",
                "severity": "critical",
                "shortfall": shortfall
            })
        
        # Check written-off companies impact
        if written_off_capital > 0:
            written_off_percentage = (written_off_capital / total_investment * 100)
            
            # Check if Coronis specifically is written off
            coronis_row = edited_df[edited_df["Company"] == "Coronis"]
            if not coronis_row.empty and float(coronis_row.iloc[0]["MOIC"]) == 0:
                coronis_investment = float(company_net_investments.get("Coronis", 0))
                other_exits_value = projected_exit_value
                
                if other_exits_value < total_investment:
                    coronis_coverage = max(0, other_exits_value - (total_investment - coronis_investment))
                    coronis_shortfall = coronis_investment - coronis_coverage
                    
                    if coronis_shortfall > 0:
                        warnings["has_warnings"] = True
                        warnings["critical_warnings"].append({
                            "type": "CORONIS_UNCOVERED",
                            "message": f"⚠️ Coronis write-off (${coronis_investment:,.0f}) cannot be fully covered. Shortfall: ${coronis_shortfall:,.0f}",
                            "severity": "critical",
                            "company": "Coronis",
                            "shortfall": coronis_shortfall
                        })
            
            if written_off_percentage > 20:
                warnings["has_warnings"] = True
                warnings["moderate_warnings"].append({
                    "type": "HIGH_WRITEOFF_PERCENTAGE",
                    "message": f"⚠️ High write-off exposure: {written_off_percentage:.1f}% of capital is written off",
                    "severity": "moderate",
                    "percentage": written_off_percentage
                })
        
        # Check for low performing scenarios
        low_performers = [r for r in company_returns if r["MOIC"] < 1.0 and r["MOIC"] > 0]
        if len(low_performers) > 3:
            warnings["has_warnings"] = True
            warnings["moderate_warnings"].append({
                "type": "MANY_UNDERPERFORMERS",
                "message": f"⚠️ {len(low_performers)} companies projected to return less than invested capital",
                "severity": "moderate",
                "count": len(low_performers)
            })
        
        # Check if net returns after fees are negative
        if net_return < 0:
            warnings["has_warnings"] = True
            warnings["moderate_warnings"].append({
                "type": "NEGATIVE_NET_RETURNS",
                "message": f"⚠️ After estimated fees, fund may have negative net returns: ${net_return:,.0f}",
                "severity": "moderate",
                "amount": net_return
            })
        
        # Add specific recommendations
        if warnings["has_warnings"]:
            recommendations = []
            
            if capital_coverage < 1.0:
                # Calculate total investment in companies with positive MOIC
                positive_moic_investment = sum(
                    float(company_net_investments.get(r["Company"], 0)) 
                    for r in company_returns 
                    if r["MOIC"] > 0
                )
                if positive_moic_investment > 0:
                    needed_moic = total_investment / positive_moic_investment
                    recommendations.append(f"To break even, performing companies need average MOIC of {needed_moic:.2f}")
            
            if written_off_capital > 0:
                positive_moic_investment = sum(
                    float(company_net_investments.get(r["Company"], 0)) 
                    for r in company_returns 
                    if r["MOIC"] > 0
                )
                if positive_moic_investment > 0:
                    coverage_needed = written_off_capital / positive_moic_investment
                    recommendations.append(f"Performing companies need extra {coverage_needed:.2f}x to cover write-offs")
            
            warnings["recommendations"] = recommendations
    
    except Exception as e:
        # If there's any error in calculation, return a safe default
        warnings["info_messages"].append({
            "type": "ERROR",
            "message": f"ℹ️ Unable to calculate some warnings: {str(e)}"
        })
    
    return warnings


def display_capital_recovery_warnings(edited_df, company_net_investments):
    """
    Display capital recovery warnings in the UI.
    """
    try:
        warnings = check_capital_recovery_warnings(edited_df, company_net_investments)
        
        if warnings["has_warnings"]:
            st.warning("### ⚠️ Capital Recovery Risk Analysis")
            
            # Display critical warnings
            if warnings["critical_warnings"]:
                for warning in warnings["critical_warnings"]:
                    st.error(warning["message"])
            
            # Display moderate warnings
            if warnings["moderate_warnings"]:
                for warning in warnings["moderate_warnings"]:
                    st.warning(warning["message"])
            
            # Display recommendations
            if "recommendations" in warnings:
                with st.expander("📊 Recommendations to Improve Returns"):
                    for rec in warnings["recommendations"]:
                        st.info(f"• {rec}")
        
    except Exception as e:
        st.error(f"Error in capital recovery analysis: {str(e)}")
        # Don't let this crash the entire app
        pass


def display_capital_recovery_analysis(fee_tracker, company_net_investments, written_off_companies):
    """
    Display capital recovery analysis including written-off company coverage.
    """
    try:
        # Get capital recovery metrics
        recovery_metrics = track_capital_recovery(fee_tracker, company_net_investments, written_off_companies)
        
        st.subheader("📊 Capital Recovery Analysis")
        
        # Summary metrics section removed per user request
        
        # Written-off companies details - HIDDEN per user request
        # try:
        #     if recovery_metrics['written_off_details']:
        #         st.subheader("Written-Off Companies")
        #         written_off_df = pd.DataFrame([
        #             {
        #                 "Company": company,
        #                 "Investment": f"${float(amount):,.0f}",  # Ensure amount is converted to float
        #                 "Status": "❌ Written Off"
        #             }
        #             for company, amount in recovery_metrics['written_off_details'].items()
        #         ])
        #         st.dataframe(written_off_df, use_container_width=True, hide_index=True)
        #
        #         # Special callout for Coronis if it's in the written-off list
        #         if "Coronis" in recovery_metrics['written_off_details']:
        #             coronis_investment = float(recovery_metrics['written_off_details']['Coronis'])  # Convert to float
        #             coronis_coverage = float(min(coronis_investment, recovery_metrics['final_written_off_coverage']))  # Convert to float
        #             coronis_shortfall = float(coronis_investment - coronis_coverage)  # Convert to float
        #
        #             if coronis_shortfall > 0:
        #                 st.error(f"""
        #                 ⚠️ **Coronis Capital Recovery Alert**
        #                 - Coronis Investment: ${coronis_investment:,.0f}
        #                 - Amount Covered by Other Exits: ${coronis_coverage:,.0f}
        #                 - **Shortfall: ${coronis_shortfall:,.0f}**
        #
        #                 The fund exit proceeds are insufficient to fully recover the capital invested in Coronis.
        #                 """)
        #             else:
        #                 st.success(f"""
        #                 ✅ **Coronis Capital Recovery**
        #                 - Coronis Investment: ${coronis_investment:,.0f}
        #                 - Fully covered by other company exits
        #                 """)
        # except Exception as e:
        #     st.error(f"Error in written-off companies section: {e}")
        #     import traceback
        #     st.error(f"Traceback: {traceback.format_exc()}")
        #     raise
        
        # Recovery timeline chart section removed
        # Create timeline_df for the detailed recovery table
        timeline_df = pd.DataFrame(recovery_metrics['recovery_timeline'])
        
        # Detailed recovery table
        try:
            with st.expander("View Detailed Recovery Timeline"):
                if not timeline_df.empty:
                    display_df = timeline_df[[
                        'Exit Date', 'Company', 'Exit Value', 'Net Proceeds', 
                        'Capital Recovered', 'Capital Shortfall', 'Written Off Shortfall', 
                        'Recovery Percentage'
                    ]].copy()
                    
                    # Format currency columns - ensure values are numeric first
                    currency_cols = ['Exit Value', 'Net Proceeds', 'Capital Recovered', 'Capital Shortfall', 'Written Off Shortfall']
                    for col in currency_cols:
                        display_df[col] = display_df[col].apply(lambda x: f"${float(x):,.0f}" if x is not None else "$0")
                    
                    display_df['Recovery Percentage'] = display_df['Recovery Percentage'].apply(lambda x: f"{float(x):.1f}%" if x is not None else "0.0%")
                    
                    st.dataframe(display_df, use_container_width=True, hide_index=True)
        except Exception as e:
            st.error(f"Error in detailed recovery table: {e}")
            import traceback
            st.error(f"Traceback: {traceback.format_exc()}")
            raise
    
    except Exception as e:
        st.error(f"Error displaying capital recovery analysis: {str(e)}")
        import traceback
        st.error(f"Full display traceback: {traceback.format_exc()}")
        # Don't let this crash the entire app
        pass
