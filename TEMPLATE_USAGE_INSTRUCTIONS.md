# Template Loading Instructions

## Template Loading Issue Resolution

The template loading functionality has been **enhanced and fixed**. Here's how to use it properly:

## How to Save a Template

1. **Set your desired values first** in the Fund Analysis section:
   - Adjust MOIC values for each company
   - Set Exit Date values for each company
   - Make sure these values are what you want to save

2. **Go to the sidebar** and expand "💾 Save Template"

3. **Enter template details**:
   - Template name (required, be descriptive)
   - Category (default: "Veritas Capital Fund VII")
   - Tags (optional, comma-separated)

4. **Click "💾 Save Template"**

5. **Look for success message**: You should see "✅ Template '[name]' saved successfully!"

## How to Load a Template

1. **Go to the sidebar** and expand "📂 Load Template"

2. **Select a template** from the dropdown list

3. **Click "Load Template"**

4. **Watch for confirmation messages**:
   - "✅ Loaded template: [name]"
   - "📝 Template values applied - data refreshed"

5. **The page will refresh automatically**

## After Loading a Template

1. **Go to the Fund Analysis section** - this is crucial!

2. **Look for confirmation message**: "✅ Applied company scenarios from template"

3. **Verify the values**:
   - Check that MOIC values match your template
   - Check that Exit Date values match your template
   - All companies should have the correct values

4. **If values don't appear correctly**:
   - Try refreshing the browser (F5)
   - Check that you're looking at the Fund Analysis section
   - Make sure the template loaded successfully (check for success messages)

## Troubleshooting

### Templates Don't Save
- **Check that you set values first**: Go to Fund Analysis and modify MOIC/Exit Date values before saving
- **Use descriptive names**: Template names must be non-empty and unique
- **Check for error messages**: Look for red error messages in the sidebar

### Templates Don't Load Properly
- **Refresh the browser**: After loading a template, refresh the page (F5)
- **Check the Fund Analysis section**: Template values are applied there
- **Look for confirmation messages**: You should see "Applied company scenarios from template"
- **Clear browser cache**: If persistent issues, clear browser cache

### Values Are Wrong After Loading
- **Check the template data**: Use the template details to verify what was saved
- **Verify you're in the right section**: Template values appear in Fund Analysis
- **Check for partial loading**: Some values might load while others don't - this usually means refresh needed

## Technical Details

### What Gets Saved in Templates
- **Target IRR**: The target return percentage
- **GP Commitment**: General Partner commitment amount
- **Management Fee Balance**: Current management fee balance
- **Management Fee Date**: Date for management fee calculation
- **Quarterly Fee**: Quarterly management fee amount
- **Company Scenarios**: For each company:
  - MOIC (Multiple of Invested Capital)
  - Exit Date (projected exit date)

### What Happens When Loading Templates
1. Template data is loaded from the database
2. Default values are updated with template values
3. **All cached data is cleared** to ensure fresh values
4. Company scenarios are stored for the Fund Analysis section
5. Page refreshes to apply new values

### File Locations
- **Database**: `templates.db` (SQLite database)
- **Template Manager**: `template_manager_sqlite.py`
- **Debug Utility**: `template_debug_utility.py`

## Debugging Commands

If you encounter issues, run these commands in the app directory:

```bash
# Test template system
python template_debug_utility.py

# Check template data
python -c "from template_manager_sqlite import SQLiteTemplateManager; tm = SQLiteTemplateManager(); print('Templates:', tm.list_templates())"

# Test loading specific template
python test_template_loading.py
```

## Recent Fixes Applied

1. **Enhanced Template Manager**: Better error handling and logging
2. **Fixed Missing Methods**: Added all required methods (get_template_statistics, export_template, import_template)
3. **Improved Cache Management**: Template loading now properly clears all cached data
4. **Better Session State Handling**: Template values persist correctly across page refreshes
5. **Enhanced Data Integrity**: Added checksums and validation for template data

## Status: WORKING ✅

The template loading functionality is now working correctly. Follow the instructions above for proper usage.
