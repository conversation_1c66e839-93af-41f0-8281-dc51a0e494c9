# enhanced_ui.py
# Enhanced UI styling for the Fund VII Exit IRR Modeler
# Based on purple-blue gradient design with modern cards and improved typography

import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
import pandas as pd

def apply_enhanced_styling():
    """Apply enhanced CSS styling to the Streamlit app"""
    st.markdown("""
    <style>
    /* Import Google Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
    
    /* Global App Styling */
    .stApp {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        font-family: 'Inter', sans-serif;
    }
    
    /* Main Container */
    .main .block-container {
        padding-top: 2rem;
        padding-bottom: 2rem;
        max-width: 100% !important;
        overflow-x: hidden !important;
    }
    
    /* Target actual Streamlit metric components */
    div[data-testid="metric-container"] {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        border-radius: 12px !important;
        padding: 20px !important;
        margin: 8px !important;
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.25) !important;
        border: 1px solid rgba(255, 255, 255, 0.15) !important;
        color: white !important;
        min-height: 120px !important;
    }
    
    div[data-testid="metric-container"] > div {
        color: white !important;
    }
    
    div[data-testid="metric-container"] label {
        color: rgba(255, 255, 255, 0.9) !important;
        font-weight: 500 !important;
        font-size: 0.875rem !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
    }
    
    div[data-testid="metric-container"] div[data-testid="metric-value"] {
        color: white !important;
        font-size: 1.75rem !important;
        font-weight: 700 !important;
    }
    
    /* Style DataFrame containers */
    div[data-testid="stDataFrame"] {
        background: white !important;
        border-radius: 16px !important;
        padding: 24px !important;
        margin: 16px 0 !important;
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3) !important;
        border: 1px solid rgba(102, 126, 234, 0.1) !important;
    }
    
    /* Style the actual dataframe table */
    .dataframe {
        border: none !important;
        border-radius: 8px !important;
        overflow: hidden !important;
    }
    
    .dataframe thead th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        color: white !important;
        border: none !important;
        padding: 12px 16px !important;
        font-weight: 600 !important;
        text-align: center !important;
    }
    
    .dataframe tbody td {
        border: 1px solid #e1e7f0 !important;
        padding: 12px 16px !important;
        text-align: center !important;
    }
    
    .dataframe tbody tr:nth-child(even) {
        background-color: #f8f9ff !important;
    }
    
    .dataframe tbody tr:hover {
        background-color: #e8edff !important;
    }
    
    /* Style chart containers */
    div[data-testid="stPlotlyChart"] {
        background: white !important;
        border-radius: 16px !important;
        padding: 24px !important;
        margin: 20px 0 !important;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2) !important;
        border: 1px solid rgba(102, 126, 234, 0.1) !important;
    }
    
    /* Style section headers */
    .stMarkdown h1, .stMarkdown h2, .stMarkdown h3 {
        color: #2d3748 !important;
        font-family: 'Inter', sans-serif !important;
        font-weight: 600 !important;
    }
    
    /* Style the title */
    .stApp > header + div h1 {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        background-clip: text !important;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
        text-align: center !important;
        padding: 20px 0 !important;
        font-size: 2.5rem !important;
        font-weight: 700 !important;
    }
    
    /* Enhanced Card Styling */
    .enhanced-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 16px;
        padding: 24px;
        margin: 16px 0;
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        color: white;
    }
    
    .enhanced-card h3 {
        color: white;
        margin-bottom: 20px;
        font-weight: 600;
        font-size: 1.25rem;
    }
    
    /* Metrics Cards */
    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 12px;
        padding: 20px;
        margin: 8px;
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.25);
        border: 1px solid rgba(255, 255, 255, 0.15);
        text-align: center;
        color: white;
        min-height: 120px;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }
    
    .metric-card .metric-label {
        font-size: 0.875rem;
        font-weight: 500;
        opacity: 0.9;
        margin-bottom: 8px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .metric-card .metric-value {
        font-size: 1.75rem;
        font-weight: 700;
        margin-bottom: 4px;
    }
    
    /* Portfolio Table Styling */
    .portfolio-table {
        background: white;
        border-radius: 12px;
        padding: 24px;
        margin: 16px 0;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(102, 126, 234, 0.1);
    }
    
    .portfolio-table .dataframe {
        border: none !important;
    }
    
    .portfolio-table .dataframe th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        color: white !important;
        border: none !important;
        padding: 12px 16px !important;
        font-weight: 600 !important;
        text-align: center !important;
    }
    
    .portfolio-table .dataframe td {
        border: 1px solid #e1e7f0 !important;
        padding: 12px 16px !important;
        text-align: center !important;
        vertical-align: middle !important;
    }
    
    .portfolio-table .dataframe tr:nth-child(even) {
        background-color: #f8f9ff !important;
    }
    
    .portfolio-table .dataframe tr:hover {
        background-color: #e8edff !important;
        transition: background-color 0.2s ease;
    }
    
    /* Waterfall Chart Container */
    .waterfall-container {
        background: white;
        border-radius: 16px;
        padding: 24px;
        margin: 20px 0;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(102, 126, 234, 0.1);
    }
    
    .waterfall-container h3 {
        color: #2d3748;
        margin-bottom: 20px;
        font-weight: 600;
        display: flex;
        align-items: center;
    }
    
    .waterfall-container h3:before {
        content: "📊";
        margin-right: 8px;
        font-size: 1.2em;
    }
    
    /* Bottom Metrics Row */
    .bottom-metrics {
        display: flex;
        gap: 16px;
        margin-top: 24px;
    }
    
    .bottom-metric-card {
        flex: 1;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 12px;
        padding: 24px;
        text-align: center;
        color: white;
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.25);
        border: 1px solid rgba(255, 255, 255, 0.15);
    }
    
    .bottom-metric-card .metric-value {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 8px;
        display: block;
    }
    
    .bottom-metric-card .metric-label {
        font-size: 0.875rem;
        font-weight: 500;
        opacity: 0.9;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    /* Enhanced Sidebar */
    .css-1d391kg {
        background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
    }
    
    .css-1d391kg .css-1v0mbdj {
        color: white;
    }
    
    /* Tab Styling */
    .stTabs [data-baseweb="tab-list"] {
        gap: 8px;
        background: white;
        border-radius: 12px;
        padding: 8px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }
    
    .stTabs [data-baseweb="tab"] {
        height: 50px;
        padding: 0px 24px;
        background: transparent;
        border-radius: 8px;
        color: #667eea;
        font-weight: 500;
        border: none;
    }
    
    .stTabs [aria-selected="true"] {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        color: white !important;
    }
    
    /* Button Styling */
    .stButton > button {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 0.5rem 1rem;
        font-weight: 500;
        transition: all 0.3s ease;
        white-space: normal;
        min-width: fit-content;
        text-align: center;
        width: 100%;
        overflow-wrap: break-word;
    }
    
    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }
    
    /* Sidebar button fix */
    .css-1d391kg .stButton > button,
    [data-testid="stSidebar"] .stButton > button {
        white-space: normal !important;
        overflow-wrap: break-word !important;
        padding: 0.5rem 0.75rem !important;
        font-size: 0.875rem !important;
        margin-bottom: 0.5rem !important;
        display: block !important;
        height: auto !important;
        min-height: 38px !important;
    }
    
    /* Fix sidebar expander content spacing */
    [data-testid="stSidebar"] [data-testid="stExpander"] > div:last-child {
        padding-top: 0.5rem !important;
    }
    
    /* Fix file uploader in sidebar */
    [data-testid="stSidebar"] [data-testid="stFileUploader"] {
        margin-bottom: 1rem !important;
    }
    
    /* Fix sidebar divider spacing */
    [data-testid="stSidebar"] .st-emotion-cache-1vbkxwb,
    [data-testid="stSidebar"] hr {
        margin: 1rem 0 !important;
    }
    
    /* Ensure proper spacing between sidebar elements */
    [data-testid="stSidebar"] > div > div > div > div {
        margin-bottom: 0.5rem;
    }
    
    /* Fix button container spacing in sidebar */
    [data-testid="stSidebar"] .element-container:has(.stButton) {
        margin-bottom: 0.75rem !important;
    }
    
    /* Align columns with buttons vertically */
    .stColumns > div {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: stretch;
    }
    
    /* Ensure buttons in columns are same height and aligned */
    .stButton {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        margin-bottom: 0;
    }
    
    /* Success/Warning/Error Styling */
    .stSuccess {
        background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
        border-radius: 8px;
        border: none;
    }
    
    .stWarning {
        background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
        border-radius: 8px;
        border: none;
    }
    
    .stError {
        background: linear-gradient(135deg, #f87171 0%, #ef4444 100%);
        border-radius: 8px;
        border: none;
    }
    
    /* Expander Styling */
    .streamlit-expanderHeader {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white !important;
        border-radius: 8px;
        font-weight: 500;
    }
    
    .streamlit-expanderContent {
        background: white;
        border-radius: 0 0 8px 8px;
        border: 1px solid rgba(102, 126, 234, 0.1);
    }
    
    /* Input Styling */
    .stSelectbox > div > div {
        background: white;
        border-radius: 8px;
        border: 1px solid rgba(102, 126, 234, 0.2);
    }
    
    .stNumberInput > div > div {
        background: white;
        border-radius: 8px;
        border: 1px solid rgba(102, 126, 234, 0.2);
    }
    
    /* Style expander headers to match theme */
    div[data-testid="stExpander"] details summary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        color: white !important;
        border-radius: 8px !important;
        font-weight: 500 !important;
        padding: 12px 16px !important;
    }
    
    div[data-testid="stExpander"] details[open] summary {
        border-radius: 8px 8px 0 0 !important;
    }
    
    div[data-testid="stExpander"] div[data-testid="stExpanderDetails"] {
        background: white !important;
        border: 1px solid rgba(102, 126, 234, 0.1) !important;
        border-radius: 0 0 8px 8px !important;
        border-top: none !important;
    }
    
    /* Bottom metrics row styling - target the columns that contain metrics */
    .element-container:has(.bottom-metric-card) {
        margin-top: 24px !important;
    }
    
    .bottom-metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        border-radius: 12px !important;
        padding: 24px !important;
        text-align: center !important;
        color: white !important;
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.25) !important;
        border: 1px solid rgba(255, 255, 255, 0.15) !important;
        margin: 8px !important;
        min-height: 100px !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
    }
    
    /* Hide Streamlit branding */
    #MainMenu {visibility: hidden;}
    footer {visibility: hidden;}
    header {visibility: hidden;}
    
    </style>
    """, unsafe_allow_html=True)

def create_enhanced_metric_card(label, value, help_text="", format_as_currency=False, format_as_percentage=False):
    """Create an enhanced metric card with gradient styling"""
    if format_as_currency:
        if isinstance(value, (int, float)):
            display_value = f"${value:,.0f}"
        else:
            display_value = str(value)
    elif format_as_percentage:
        if isinstance(value, (int, float)):
            display_value = f"{value:.2f}%"
        else:
            display_value = str(value)
    else:
        display_value = str(value)
    
    card_html = f"""
    <div class="metric-card" title="{help_text}">
        <div class="metric-label">{label}</div>
        <div class="metric-value">{display_value}</div>
    </div>
    """
    return card_html

def create_enhanced_portfolio_table(df):
    """Create an enhanced portfolio table with modern styling"""
    # Format the dataframe for display
    df_display = df.copy()
    
    # Apply formatting to numeric columns
    numeric_columns = df_display.select_dtypes(include=['float64', 'int64']).columns
    for col in numeric_columns:
        if 'value' in col.lower() or 'investment' in col.lower():
            df_display[col] = df_display[col].apply(lambda x: f"${x:,.0f}" if pd.notnull(x) else "$0")
        elif 'moic' in col.lower():
            df_display[col] = df_display[col].apply(lambda x: f"{x:.2f}x" if pd.notnull(x) else "0.00x")
    
    return df_display

def create_enhanced_waterfall_chart(categories, values, title="Fund Exit Value Waterfall"):
    """Create an enhanced waterfall chart with modern styling"""
    
    # Define colors for different types of values
    colors = []
    for i, val in enumerate(values):
        if i == 0:  # Starting value
            colors.append('#667eea')
        elif val > 0:  # Positive values
            colors.append('#4ade80')
        else:  # Negative values
            colors.append('#ef4444')
    
    # Create the waterfall chart
    fig = go.Figure()
    
    # Calculate cumulative values for positioning
    cumulative = 0
    x_vals = []
    y_vals = []
    bases = []
    
    for i, val in enumerate(values):
        x_vals.append(categories[i])
        y_vals.append(abs(val))
        if i == 0:
            bases.append(0)
        else:
            bases.append(cumulative)
        cumulative += val
    
    # Add bars
    for i in range(len(categories)):
        fig.add_trace(go.Bar(
            name=categories[i],
            x=[categories[i]],
            y=[y_vals[i]],
            base=[bases[i]],
            marker_color=colors[i],
            text=[f"${values[i]:,.0f}"],
            textposition="middle center",
            textfont=dict(color="white", size=12, family="Inter"),
            hovertemplate=f"<b>{categories[i]}</b><br>Value: ${values[i]:,.0f}<extra></extra>",
            showlegend=False
        ))
    
    # Update layout
    fig.update_layout(
        title={
            'text': title,
            'x': 0.5,
            'xanchor': 'center',
            'font': {'size': 20, 'family': 'Inter', 'color': '#2d3748'}
        },
        xaxis=dict(
            title="",
            tickangle=45,
            tickfont=dict(size=11, family='Inter'),
            gridcolor='rgba(0,0,0,0.1)'
        ),
        yaxis=dict(
            title="Value ($)",
            tickformat='$,.0f',
            tickfont=dict(size=11, family='Inter'),
            gridcolor='rgba(0,0,0,0.1)'
        ),
        plot_bgcolor='white',
        paper_bgcolor='white',
        height=400,
        margin=dict(l=60, r=60, t=80, b=100),
        font=dict(family='Inter')
    )
    
    return fig

def create_bottom_metrics_row(metrics_data):
    """Create a row of bottom metrics with gradient cards like in the target image"""
    cols = st.columns(len(metrics_data))
    
    for i, (label, value, format_type) in enumerate(metrics_data):
        with cols[i]:
            if format_type == 'currency':
                display_value = f"${value:,.0f}" if isinstance(value, (int, float)) else str(value)
            elif format_type == 'percentage':
                display_value = f"{value:.1f}%" if isinstance(value, (int, float)) else str(value)
            elif format_type == 'multiplier':
                display_value = f"{value:.2f}x" if isinstance(value, (int, float)) else str(value)
            else:
                display_value = str(value)
            
            st.markdown(f"""
            <div class="bottom-metric-card">
                <div style="font-size: 2rem; font-weight: 700; margin-bottom: 8px;">{display_value}</div>
                <div style="font-size: 0.875rem; font-weight: 500; opacity: 0.9; text-transform: uppercase; letter-spacing: 0.5px;">{label}</div>
            </div>
            """, unsafe_allow_html=True)

def create_custom_bottom_metrics(irr_value, moic_value, gp_profits_value):
    """Create the specific bottom metrics row as shown in target image"""
    st.markdown("""
    <div style="display: flex; gap: 16px; margin-top: 32px;">
        <div class="bottom-metric-card" style="flex: 1;">
            <div style="font-size: 2rem; font-weight: 700; margin-bottom: 8px;">{}</div>
            <div style="font-size: 0.875rem; font-weight: 500; opacity: 0.9; text-transform: uppercase; letter-spacing: 0.5px;">NET FUND IRR</div>
        </div>
        <div class="bottom-metric-card" style="flex: 1;">
            <div style="font-size: 2rem; font-weight: 700; margin-bottom: 8px;">{}</div>
            <div style="font-size: 0.875rem; font-weight: 500; opacity: 0.9; text-transform: uppercase; letter-spacing: 0.5px;">NET FUND MOIC</div>
        </div>
        <div class="bottom-metric-card" style="flex: 1;">
            <div style="font-size: 2rem; font-weight: 700; margin-bottom: 8px;">{}</div>
            <div style="font-size: 0.875rem; font-weight: 500; opacity: 0.9; text-transform: uppercase; letter-spacing: 0.5px;">GP PROFITS</div>
        </div>
    </div>
    """.format(
        f"{irr_value:.1f}%" if isinstance(irr_value, (int, float)) else str(irr_value),
        f"{moic_value:.2f}x" if isinstance(moic_value, (int, float)) else str(moic_value),
        f"${gp_profits_value:,.0f}" if isinstance(gp_profits_value, (int, float)) else str(gp_profits_value)
    ), unsafe_allow_html=True)

def create_enhanced_section_header(title, icon="📊"):
    """Create an enhanced section header with icon"""
    st.markdown(f"""
    <div style="margin: 32px 0 20px 0;">
        <h2 style="color: #2d3748; font-weight: 600; font-size: 1.5rem; margin: 0; display: flex; align-items: center;">
            <span style="margin-right: 8px; font-size: 1.2em;">{icon}</span>
            {title}
        </h2>
    </div>
    """, unsafe_allow_html=True)

def wrap_in_enhanced_card(content_func, title="", icon=""):
    """Wrap content in an enhanced card container"""
    st.markdown(f"""
    <div class="enhanced-card">
        {f'<h3><span style="margin-right: 8px;">{icon}</span>{title}</h3>' if title else ''}
    """, unsafe_allow_html=True)
    
    content_func()
    
    st.markdown("</div>", unsafe_allow_html=True)

def wrap_in_waterfall_container(content_func, title=""):
    """Wrap waterfall chart in enhanced container"""
    st.markdown(f"""
    <div class="waterfall-container">
        {f'<h3>{title}</h3>' if title else ''}
    """, unsafe_allow_html=True)
    
    content_func()
    
    st.markdown("</div>", unsafe_allow_html=True)

def wrap_in_portfolio_table(content_func, title=""):
    """Wrap portfolio table in enhanced container"""
    st.markdown(f"""
    <div class="portfolio-table">
        {f'<h3 style="color: #2d3748; margin-bottom: 20px; font-weight: 600;">{title}</h3>' if title else ''}
    """, unsafe_allow_html=True)
    
    content_func()
    
    st.markdown("</div>", unsafe_allow_html=True)