# IPO Scenario Feature Documentation

## Overview
The IPO Scenario feature has been successfully implemented in the IRR model, allowing users to model initial public offerings (IPOs) with bi-yearly distributions over a 3-year period.

## Implementation Status

### Phase 1: Basic IPO Selection (✅ COMPLETED)
- Added IPO scenario module (`ipo_scenario.py`)
- Integrated IPO selection checkboxes in fund analysis tab
- Implemented equal distribution calculation
- Created basic distribution table display

### Phase 2: Advanced Distribution Patterns (✅ COMPLETED - June 29, 2025)
- Added distribution frequency options (quarterly/semi-annual/annual)
- Implemented custom percentage allocation with validation
- Added three fee allocation methods (first/pro-rata/realized)
- Created interactive distribution timeline visualization
- Enhanced UI with Select All/Clear All and configuration options
- Added detailed distribution views with fee breakdowns

### Phase 3: Visualization & Analysis (✅ COMPLETED - June 29, 2025)
- Created distribution timeline Gantt chart
- Implemented cash flow comparison visualizations
- Added DPI (Distributions to Paid-In) progression tracking
- Built time-weighted returns analysis
- Developed comprehensive scenario comparison metrics
- Integrated analytics dashboard with tabbed interface

### Files Modified
1. **New Files Created:**
   - `ipo_scenario.py` - Core IPO scenario functionality
   - `test_ipo_scenario.py` - Test script for IPO calculations
   - `tab_fund_analysis_original.py` - Backup of original fund analysis
   - `IPO_Scenario_Implementation_Plan.md` - Implementation plan

2. **Modified Files:**
   - `tab_fund_analysis.py` - Added IPO scenario section and integration
   - `excel_export_helpers.py` - Added IPO scenario export functionality
   - `requirements.txt` - Added python-dateutil dependency

## Key Features Implemented

### 1. IPO Selection Interface
- Checkbox-based selection for each eligible company
- Filters out written-off companies (MOIC = 0)
- Visual indicators for selected companies

### 2. Distribution Patterns
- **Equal Distribution**: Splits exit value equally across 6 distributions
- **Front-Loaded**: Higher distributions early (25%, 20%, 15%, 15%, 15%, 10%)
- **Back-Loaded**: Higher distributions later (10%, 15%, 15%, 15%, 20%, 25%)

### 3. Distribution Schedule
- Bi-yearly (semi-annual) distributions over 3 years
- 6 total distributions per company
- Automatic date calculation from exit date

### 4. Cash Flow Integration
- IPO distributions replace single exit cash flows
- Proper aggregation for IRR calculations
- Comparison metrics between standard exit and IPO scenarios

### 5. UI Components
- Enable/disable toggle for IPO scenario
- Pattern selector (equal/front/back)
- Distribution preview table
- Expandable distribution dates view
- Scenario comparison metrics

## Usage Instructions

### Enabling IPO Scenario:
1. Navigate to the Fund Analysis tab
2. After the Exit Plan Editor, find the "IPO Scenario Analysis" section
3. Check "Enable IPO Scenario"
4. Select distribution pattern (Equal, Front-Loaded, or Back-Loaded)
5. Check the boxes next to companies you want to model as IPOs
6. View the distribution schedule preview
7. Click "Calculate Results" to update the fund analysis

### Viewing Results:
- When IPO scenario is active, you'll see:
  - Scenario comparison showing Standard Exit vs IPO Scenario metrics
  - IRR impact of the delayed distributions
  - Distribution timeline for each selected company

### Exporting Data:
- IPO distribution schedules can be exported to Excel
- The export includes:
  - Summary of IPO companies and settings
  - Detailed distribution schedule with dates and amounts
  - Formatted for easy review and sharing

## Technical Details

### Cash Flow Modification:
```python
# Original single exit: (exit_date, exit_value)
# IPO scenario: 6 distributions over 3 years
# Distribution 1: (exit_date + 6 months, exit_value * pct1)
# Distribution 2: (exit_date + 12 months, exit_value * pct2)
# ... etc
```

### Session State Management:
- `ipo_selected_companies`: Set of selected company names
- `ipo_distribution_pattern`: Selected distribution pattern
- `ipo_scenario_changed`: Flag to trigger recalculation

## Future Enhancements (Phases 2-4)

### Phase 2: Advanced Distribution Patterns
- Custom percentage allocation
- Variable distribution periods
- Fee allocation options

### Phase 3: Visualization & Analysis
- Timeline visualization
- Cash flow comparison charts
- Advanced metrics (DPI progression, time-weighted returns)

### Phase 4: Export & Reporting
- Enhanced Excel exports
- PDF report integration
- Distribution calendar export

## Testing

Run the test script to verify IPO calculations:
```bash
python test_ipo_scenario.py
```

This will demonstrate:
- Distribution calculations for all patterns
- Date generation
- DataFrame creation

## Notes
- Management fees are currently deducted at the original exit date
- Future enhancement could distribute fees across IPO distributions
- The feature respects existing fee calculations and capital call logic