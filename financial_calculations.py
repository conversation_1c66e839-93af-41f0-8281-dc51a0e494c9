# financial_calculations.py
# Updated version with actual capital call fee integration ($389M total)
# Now uses pyxirr library to match Excel's XIRR function exactly

from datetime import datetime, timedelta
from math import pow
from collections import defaultdict
import pandas as pd
import pyxirr
from capital_call_fees import (
    load_capital_call_fees, 
    calculate_management_fees_with_capital_calls,
    calculate_fund_fees_schedule_with_capital_calls,
    get_capital_call_summary_fast,
    clear_capital_call_cache
)

# Global variable to cache capital call data
_capital_call_data_cache = None

def get_capital_call_data():
    """Get cached capital call data or load it."""
    global _capital_call_data_cache
    if _capital_call_data_cache is None:
        _capital_call_data_cache = load_capital_call_fees()
    return _capital_call_data_cache

def calculate_company_management_fees_with_hurdle(
    balance_date, exit_date, total_initial_balance, company_name, 
    company_fees=None, fee_mapping=None, hurdle_rate=0.08, 
    include_capital_calls=True, is_first_exit=True
):
    """
    Calculate management fees for a specific company including capital call fees.
    Priority returns are ONLY calculated on capital call fees, not management fees.
    Updated to use actual Excel data: $289,537,026 principal + $99,528,942 PR.
    """
    if exit_date <= balance_date:
        return {
            'total_fees': 0,
            'initial_balance': 0,
            'new_fees': 0,
            'priority_return': 0,
            'days': 0,
            'fee_basis': 0,
            'company_quarterly_fee': 0,
            'company_proportion': 0,
            'capital_call_fees': 0,
            'capital_call_pr': 0,
            'mgmt_fees_only': 0
        }
    
    # Load company fees if not provided
    if company_fees is None or fee_mapping is None:
        from data_loader import load_company_management_fees
        company_fees, fee_mapping = load_company_management_fees()
    
    # Get company-specific quarterly fee
    from data_loader import get_company_quarterly_fee
    company_quarterly_fee = get_company_quarterly_fee(company_name, company_fees, fee_mapping)
    
    # Calculate days between dates
    days_diff = (exit_date - balance_date).days
    
    # Calculate number of quarters
    quarters = days_diff / 91.25
    
    # Calculate new fees accrued since balance date using company-specific fee
    new_fees = company_quarterly_fee * quarters
    
    # Company's share of initial balance (proportional to their quarterly fee)
    total_fund_quarterly_fee = sum(company_fees.values())
    company_proportion = company_quarterly_fee / total_fund_quarterly_fee if total_fund_quarterly_fee > 0 else 0
    company_initial_balance = total_initial_balance * company_proportion
    
    # Total management fee basis for this company
    total_mgmt_fee_basis = company_initial_balance + new_fees
    
    # NO priority return on management fees
    total_mgmt_fees = total_mgmt_fee_basis
    
    # Calculate capital call fees if requested AND if this is the first exit
    capital_call_amount = 0
    capital_call_pr = 0
    
    if include_capital_calls and is_first_exit:
        capital_call_data = get_capital_call_data()
        from capital_call_fees import calculate_capital_call_fees_at_exit
        
        # Calculate capital call fees using actual Excel data
        capital_call_results = calculate_capital_call_fees_at_exit(
            capital_call_data, exit_date, is_first_exit, hurdle_rate
        )
        
        # Apply company proportion to capital call fees and PR
        # Using actual amounts: $289,537,026 principal + $99,528,942 PR
        capital_call_amount = capital_call_results['total_satisfied_amount'] * company_proportion
        capital_call_pr = capital_call_results['total_satisfied_pr'] * company_proportion
    
    # Total fees including capital calls
    total_fees_with_capital_calls = total_mgmt_fees + capital_call_amount + capital_call_pr
    
    # Priority return is ONLY from capital calls
    total_priority_return = capital_call_pr
    
    return {
        'total_fees': total_fees_with_capital_calls,
        'initial_balance': company_initial_balance,
        'new_fees': new_fees,
        'priority_return': total_priority_return,  # Only capital call PR
        'days': days_diff,
        'fee_basis': total_mgmt_fee_basis,
        'company_quarterly_fee': company_quarterly_fee,
        'company_proportion': company_proportion,
        'capital_call_fees': capital_call_amount,
        'capital_call_pr': capital_call_pr,
        'mgmt_fees_only': total_mgmt_fees,
        'is_first_exit': is_first_exit
    }


def calculate_management_fees_with_hurdle(
    balance_date, exit_date, initial_balance, quarterly_fee,
    hurdle_rate=0.08, include_capital_calls=True, is_first_exit=True,
    fee_basis_adjustment=0
):
    """
    Calculate management fees and optionally capital call fees.
    Priority returns are ONLY calculated on capital call fees, NOT management fees.
    Updated to use actual Excel data: $289,537,026 principal + $99,528,942 PR.
    """
    # Handle NaT (Not a Time) values - return zero fees
    if pd.isna(exit_date) or exit_date is pd.NaT:
        return {
            'total_fees': 0,
            'initial_balance': initial_balance,
            'new_fees': 0,
            'priority_return': 0,
            'days': 0,
            'fee_basis': initial_balance,
            'mgmt_fees_only': 0,
            'capital_call_amount': 0,
            'capital_call_pr': 0,
            'mgmt_priority_return': 0,
            'is_first_exit': is_first_exit
        }
    
    if not include_capital_calls:
        # Original calculation without capital calls and NO priority return
        if exit_date <= balance_date:
            return {
                'total_fees': 0,
                'initial_balance': initial_balance,
                'new_fees': 0,
                'priority_return': 0,
                'days': 0,
                'fee_basis': initial_balance,
                'mgmt_fees_only': 0,
                'capital_call_amount': 0,
                'capital_call_pr': 0
            }
        
        days_diff = (exit_date - balance_date).days
        quarters = days_diff / 91.25
        new_fees = quarterly_fee * quarters
        total_fee_basis = initial_balance + new_fees - fee_basis_adjustment
        
        # NO priority return on management fees
        return {
            'total_fees': total_fee_basis,  # Just the fees, no PR
            'initial_balance': initial_balance,
            'new_fees': new_fees,
            'priority_return': 0,  # No PR on management fees
            'days': days_diff,
            'fee_basis': total_fee_basis,
            'mgmt_fees_only': total_fee_basis,
            'capital_call_amount': 0,
            'capital_call_pr': 0
        }
    
    # New calculation including capital calls (only at first exit)
    capital_call_data = get_capital_call_data()
    fee_details = calculate_management_fees_with_capital_calls(
        balance_date, exit_date, initial_balance, quarterly_fee, 
        capital_call_data, hurdle_rate, is_first_exit
    )
    
    # Return in the expected format with actual Excel amounts
    return {
        'total_fees': fee_details['total_fees'],
        'initial_balance': initial_balance,
        'new_fees': fee_details['mgmt_new_fees'],
        'priority_return': fee_details['total_priority_return'],  # Only capital call PR
        'days': fee_details['days'],
        'fee_basis': fee_details['total_basis'],  # Combined basis
        
        # Additional fields for detailed breakdown with actual amounts
        'mgmt_priority_return': 0,  # No PR on management fees
        'capital_call_amount': fee_details['capital_call_satisfied_amount'],  # $289,537,026
        'capital_call_pr': fee_details['capital_call_satisfied_pr'],  # $99,528,942
        'mgmt_fees_only': fee_details['mgmt_total_fees'],
        'is_first_exit': is_first_exit
    }


def calculate_fund_fees_schedule(
    exits_df, initial_fee_balance, fee_balance_date, quarterly_fee, 
    hurdle_rate=0.08, include_capital_calls=True
):
    """
    Calculate the fee schedule for multiple exits, optionally including capital call fees.
    Updated to use actual Excel data showing $389M total capital call fees.
    """
    if include_capital_calls:
        capital_call_data = get_capital_call_data()
        return calculate_fund_fees_schedule_with_capital_calls(
            exits_df, initial_fee_balance, fee_balance_date, 
            quarterly_fee, capital_call_data, hurdle_rate
        )
    
    # Original calculation without capital calls
    exits_sorted = exits_df.sort_values('Exit Date').reset_index(drop=True)
    
    fee_schedule = []
    current_balance = initial_fee_balance
    current_date = fee_balance_date
    
    for idx, exit in exits_sorted.iterrows():
        exit_date = pd.to_datetime(exit['Exit Date'])
        
        fee_details = calculate_management_fees_with_hurdle(
            current_date, exit_date, current_balance, 
            quarterly_fee, hurdle_rate, include_capital_calls=False
        )
        
        fee_schedule.append({
            'Exit Number': idx + 1,
            'Company': exit['Company'],
            'Exit Date': exit_date,
            'Exit Value': exit.get('Exit Value', 0),
            'Days Since Last Payment': fee_details['days'],
            'Starting Fee Balance': current_balance,
            'New Fees Accrued': fee_details['new_fees'],
            'Total Fee Basis': fee_details['fee_basis'],
            'Priority Return (8%)': fee_details['priority_return'],
            'Total Fees Due': fee_details['total_fees'],
            'Net Exit Value': exit.get('Exit Value', 0) - fee_details['total_fees']
        })
        
        current_balance = 0
        current_date = exit_date
    
    return fee_schedule


# Keep all other existing functions unchanged
def xirr(cash_flows, guess=0.1, max_iterations=100):
    """
    Calculate the Internal Rate of Return using Excel's XIRR function via pyxirr library.
    This replaces the Newton-Raphson method to match Excel's XIRR behavior exactly.
    """
    if not cash_flows or len(cash_flows) < 2:
        return None
    
    try:
        # Convert cash flows to the format expected by pyxirr
        dates = []
        values = []
        
        for date, value in cash_flows:
            dates.append(date)
            values.append(value)
        
        # Use pyxirr which matches Excel's XIRR function exactly
        irr = pyxirr.xirr(dates, values)
        
        return irr
    except Exception as e:
        # If pyxirr fails, return None (same behavior as before)
        return None


def aggregate_cashflows_by_date(cash_flows):
    """Aggregate cash flows by date."""
    aggregated = defaultdict(float)
    
    for date, amount in cash_flows:
        aggregated[date] += amount
        
    return sorted([(date, amount) for date, amount in aggregated.items()])


def calculate_fund_moic(data, fee_tracker, edited_df, gp_commitment=0, company_net_investments=None):
    """Calculate the fund-level MOIC using a consistent method."""
    
    # Use company_net_investments if provided for total investment calculation
    if company_net_investments is not None:
        # Calculate total investment from company net investments
        total_raw_investment = sum(company_net_investments.values())
        total_investment = max(0, total_raw_investment - gp_commitment)
    else:
        # Fallback to original method for backward compatibility
        total_investment = 0
        
        # Get all contributions (investments)
        for _, row in data.iterrows():
            if row["Contributions"] < 0:
                total_investment += abs(row["Contributions"])
        
        # Deduct GP commitment from total investment
        total_investment = max(0, total_investment - gp_commitment)
    
    # Calculate total value (from exits or current book values)
    total_value = 0
    
    # Get all realized values from tracker
    exited_companies = set()
    for exit_info in fee_tracker:
        company = exit_info["Company"]
        exit_value = exit_info["Exit Value"]
        total_fees = exit_info.get("Total Fees", 0)
        
        # Add exit value minus fees
        total_value += exit_value - total_fees
        exited_companies.add(company)
    
    # For companies that haven't exited, use their current book value
    for company in edited_df["Company"]:
        if company not in exited_companies:
            comp_data = data[data["Deal Name"] == company]
            book_row = comp_data[comp_data["Book Value"] > 0].sort_values("Date", ascending=False)
            
            if not book_row.empty:
                book_val = book_row.iloc[0]["Book Value"]
                total_value += book_val
    
    # Calculate MOIC
    if total_investment > 0:
        moic = total_value / total_investment
    else:
        moic = 0
    
    return moic, total_investment, total_value


def calculate_gp_carry(data_df, total_value, hurdle_rate=0.08, carry_percentage=0.20, exit_timeline=None, gp_commitment=0, company_net_investments=None, call_dates=None):
    """Calculate GP carry based on profits above the time-weighted hurdle rate."""
    import pandas as pd
    
    # Use company_net_investments if provided, otherwise fall back to data_df parsing
    if company_net_investments is not None:
        # Calculate total investment from company net investments
        total_raw_investment = sum(company_net_investments.values())
        total_investment = max(0, total_raw_investment - gp_commitment)
        total_distributions = 0  # Already netted in company_net_investments
    else:
        # Fallback to original method for backward compatibility
        total_investment = 0
        total_distributions = 0
        
        # Get all contributions (investments) and distributions
        for _, row in data_df.iterrows():
            if row["Contributions"] < 0:  # Negative contributions are investments
                total_investment += abs(row["Contributions"])
            elif row["Contributions"] > 0:  # Positive contributions are distributions
                total_distributions += row["Contributions"]
        
        # Calculate the adjustment factor to account for GP commitment (same as breakdown function)
        total_raw_investment = total_investment
        net_total_investment = max(0, total_investment - gp_commitment)
        adjustment_factor = net_total_investment / total_raw_investment if total_raw_investment > 0 else 0
        
        # Deduct GP commitment from total investment for final calculations
        total_investment = net_total_investment
    
    # Calculate net investment (total investments minus total distributions)
    net_investment = total_investment - total_distributions
    net_investment = max(0, net_investment)  # Don't allow negative net investment
    
    # Calculate time-weighted hurdle return using the same methodology as calculate_hurdle_return_breakdown
    if exit_timeline is not None and not exit_timeline.empty:
        try:
            # Use the calculate_hurdle_return_breakdown function for consistent results
            hurdle_breakdown_df = calculate_hurdle_return_breakdown(
                data_df, 
                exit_timeline=exit_timeline, 
                hurdle_rate=hurdle_rate, 
                gp_commitment=gp_commitment, 
                call_dates=call_dates  # Pass the call_dates parameter
            )
            
            # Get the Fund Total hurdle return
            fund_total_row = hurdle_breakdown_df[hurdle_breakdown_df['Company'] == 'FUND TOTAL']
            if not fund_total_row.empty:
                hurdle_return = fund_total_row['Hurdle_Return'].iloc[0]
            else:
                # Fallback to summing company totals
                company_totals = hurdle_breakdown_df[
                    (hurdle_breakdown_df['Investment_Date'] == 'TOTAL') & 
                    (hurdle_breakdown_df['Company'] != 'FUND TOTAL')
                ]
                hurdle_return = company_totals['Hurdle_Return'].sum()
                
        except Exception as e:
            # Fallback to 1-year hurdle calculation if breakdown function fails
            hurdle_return = total_investment * (pow(1 + hurdle_rate, 1) - 1)
    else:
        # Fallback to 1-year hurdle calculation if no exit timeline provided
        hurdle_return = total_investment * (pow(1 + hurdle_rate, 1) - 1)
    
    # Calculate Priority Return from capital calls and add to hurdle return
    priority_return_from_capital_calls = 0
    if exit_timeline is not None and not exit_timeline.empty:
        try:
            # Get the first exit date to calculate priority return
            first_exit_date = exit_timeline['Exit_Date'].min()
            
            # Calculate capital call priority return for the first exit
            from capital_call_fees import get_capital_call_data, calculate_capital_call_fees_at_exit
            capital_call_data = get_capital_call_data()
            capital_call_results = calculate_capital_call_fees_at_exit(
                capital_call_data, first_exit_date, is_first_exit=True, hurdle_rate=hurdle_rate
            )
            priority_return_from_capital_calls = capital_call_results.get('total_satisfied_pr', 0)
        except Exception as e:
            # If capital call calculation fails, use zero priority return
            priority_return_from_capital_calls = 0
    
    # Add Priority Return to the hurdle return calculation
    total_hurdle_return = hurdle_return + priority_return_from_capital_calls
    
    # Calculate total profit
    total_profit = total_value - total_investment
    
    # Calculate excess profit (profit above hurdle including priority return)
    excess_profit = max(0, total_profit - total_hurdle_return)
    
    # Calculate GP carry
    gp_carry = excess_profit * carry_percentage
    
    # Calculate GP catchup (20% of total priority return)
    # This includes both fund-level hurdle return and any capital call priority returns
    gp_catchup = total_hurdle_return * carry_percentage
    
    # Calculate total GP compensation (GP carry + GP catchup)
    total_gp_compensation = gp_carry + gp_catchup
    
    return {
        'total_profit': total_profit,
        'hurdle_return': hurdle_return,
        'priority_return_from_capital_calls': priority_return_from_capital_calls,
        'total_hurdle_return': total_hurdle_return,
        'excess_profit': excess_profit,
        'gp_carry': gp_carry,
        'gp_catchup': gp_catchup,
        'total_gp_compensation': total_gp_compensation,
        'total_investment': total_investment,
        'net_investment': net_investment,
        'total_distributions': total_distributions,
        'hurdle_rate_used': hurdle_rate
    }


# Backward compatibility wrapper
def calculate_management_fees(balance_date, exit_date, initial_balance, quarterly_fee):
    """
    Wrapper function for backward compatibility.
    Calls the new function without capital calls and returns just the total fees.
    """
    result = calculate_management_fees_with_hurdle(
        balance_date, exit_date, initial_balance, quarterly_fee, 
        hurdle_rate=0.08, include_capital_calls=False
    )
    return result['total_fees']


# Additional utility function for capital call fee display
def get_capital_call_summary():
    """
    Get a summary of capital call fees for display purposes.
    Returns the actual Excel amounts.
    """
    capital_call_data = get_capital_call_data()
    
    return {
        'total_principal': capital_call_data.get('total_capital_called', 288277746),
        'total_priority_return': capital_call_data.get('total_priority_return', 99528942),
        'total_fees': capital_call_data.get('total_capital_called', 288277746) + 
                     capital_call_data.get('total_priority_return', 99528942),
        'number_of_calls': len(capital_call_data.get('capital_calls', [])),
        'distribution_date': capital_call_data.get('distribution_date', pd.Timestamp('2025-09-30'))
    }

def calculate_management_fees_with_hurdle_fast(balance_date, exit_date, initial_balance, quarterly_fee, hurdle_rate=0.08, include_capital_calls=True, is_first_exit=True):
    """
    Fast version of management fees calculation optimized for sensitivity analysis.
    Returns only essential totals without detailed breakdowns.
    """
    # Handle NaT (Not a Time) values - return zero fees
    if pd.isna(exit_date) or exit_date is pd.NaT:
        return {
            'total_fees': 0,
            'mgmt_fees_only': 0,
            'capital_call_amount': 0,
            'capital_call_pr': 0,
            'priority_return': 0,
            'new_fees': 0,
            'days': 0,
            'fee_basis': initial_balance
        }
    
    if exit_date <= balance_date:
        mgmt_fees = initial_balance
        new_fees = 0
        days = 0
    else:
        days = (exit_date - balance_date).days
        quarters = days / 91.25
        new_fees = quarterly_fee * quarters
        mgmt_fees = initial_balance + new_fees
    
    # Priority return calculation (8% hurdle on fee basis)
    if days > 0:
        priority_return = mgmt_fees * (pow(1 + hurdle_rate, days / 365) - 1)
    else:
        priority_return = 0
    
    total_mgmt_with_pr = mgmt_fees + priority_return
    
    # Capital call fees (only at first exit)
    capital_call_amount = 0
    capital_call_pr = 0
    
    if include_capital_calls and is_first_exit:
        capital_call_data = get_capital_call_data()
        capital_call_summary = get_capital_call_summary_fast(capital_call_data, exit_date, is_first_exit, hurdle_rate)
        capital_call_amount = capital_call_summary['total_satisfied_amount']
        capital_call_pr = capital_call_summary['total_satisfied_pr']
    
    total_fees = total_mgmt_with_pr + capital_call_amount + capital_call_pr
    
    return {
        'total_fees': total_fees,
        'mgmt_fees_only': total_mgmt_with_pr,
        'capital_call_amount': capital_call_amount,
        'capital_call_pr': capital_call_pr,
        'priority_return': priority_return,
        'new_fees': new_fees,
        'days': days,
        'fee_basis': mgmt_fees
    }

def calculate_hurdle_return_breakdown(data_df, exit_timeline=None, hurdle_rate=0.08, gp_commitment=0, call_dates=None):
    """
    Calculate detailed hurdle return breakdown by company.
    Returns a DataFrame with company-level hurdle return details.
    Now supports multiple call dates per company (e.g., Peraton and HMH).
    
    Args:
        data_df: DataFrame with investment data
        exit_timeline: DataFrame with exit information
        hurdle_rate: Annual hurdle rate (default 8%)
        gp_commitment: GP commitment amount to subtract from total investment
        call_dates: Dictionary mapping company names to their call dates from Investment PR tab
                   Can contain single dates or lists of dates for companies with multiple calls
    """
    import pandas as pd
    
    breakdown_data = []
    
    if exit_timeline is None or exit_timeline.empty:
        # If no exit timeline, return empty breakdown
        return pd.DataFrame(columns=[
            'Company', 'Investment_Date', 'Investment_Amount', 'Exit_Date', 
            'Days_Held', 'Hurdle_Rate', 'Hurdle_Return'
        ])
    
    # First, calculate total investment to determine GP commitment proportion
    total_raw_investment = 0
    for _, row in data_df.iterrows():
        if row["Contributions"] < 0:  # This is an investment
            total_raw_investment += abs(row["Contributions"])
    
    # Calculate the adjustment factor to account for GP commitment
    # Net investment = Total investment - GP commitment
    net_total_investment = max(0, total_raw_investment - gp_commitment)
    adjustment_factor = net_total_investment / total_raw_investment if total_raw_investment > 0 else 0
    
    # Group investments by company and calculate net investment amounts
    company_investments = {}
    
    for _, row in data_df.iterrows():
        if row["Contributions"] < 0:  # This is an investment
            company = row["Deal Name"]
            raw_investment_amount = abs(row["Contributions"])
            # Adjust investment amount to account for GP commitment
            adjusted_investment_amount = raw_investment_amount * adjustment_factor
            
            if company not in company_investments:
                company_investments[company] = {
                    'total_investment': 0,
                    'call_dates': [],
                    'call_amounts': []
                }
            
            company_investments[company]['total_investment'] += adjusted_investment_amount
    
    # Use call dates from Investment PR tab if available, otherwise fall back to cash flow dates
    for company in company_investments:
        if call_dates and company in call_dates:
            # Check if we have detailed call info with amounts
            call_info_key = f"{company}_call_info"
            if call_info_key in call_dates:
                # Use detailed call information with actual amounts from Investment PR
                # These amounts should NOT be adjusted by GP commitment factor
                call_info = call_dates[call_info_key]
                if isinstance(call_info, list):
                    # Multiple calls with amounts - use Investment PR amounts directly
                    company_investments[company]['call_dates'] = []
                    company_investments[company]['call_amounts'] = []
                    total_pr_amount = 0
                    for call in call_info:
                        company_investments[company]['call_dates'].append(pd.to_datetime(call['call_date']))
                        company_investments[company]['call_amounts'].append(call['investment_amount'])
                        total_pr_amount += call['investment_amount']
                    # Update total_investment to reflect Investment PR amounts
                    company_investments[company]['total_investment'] = total_pr_amount
                else:
                    # Single call with amount - use Investment PR amount directly
                    company_investments[company]['call_dates'] = [pd.to_datetime(call_info['call_date'])]
                    company_investments[company]['call_amounts'] = [call_info['investment_amount']]
                    # Update total_investment to reflect Investment PR amount
                    company_investments[company]['total_investment'] = call_info['investment_amount']
            else:
                # Fall back to just dates (backward compatibility)
                company_call_dates = call_dates[company]
                if isinstance(company_call_dates, list):
                    # Multiple call dates - use all of them with equal split
                    company_investments[company]['call_dates'] = [pd.to_datetime(date) for date in company_call_dates]
                    # Split total investment equally since we don't have amounts
                    total_investment = company_investments[company]['total_investment']
                    equal_amount = total_investment / len(company_call_dates)
                    company_investments[company]['call_amounts'] = [equal_amount] * len(company_call_dates)
                else:
                    # Single call date
                    company_investments[company]['call_dates'] = [pd.to_datetime(company_call_dates)]
                    company_investments[company]['call_amounts'] = [company_investments[company]['total_investment']]
        else:
            # Fall back to earliest cash flow date for this company
            comp_data = data_df[data_df["Deal Name"] == company]
            investment_rows = comp_data[comp_data["Contributions"] < 0]
            if not investment_rows.empty:
                earliest_cash_flow_date = investment_rows["Date"].min()
                company_investments[company]['call_dates'] = [pd.to_datetime(earliest_cash_flow_date)]
                company_investments[company]['call_amounts'] = [company_investments[company]['total_investment']]
    
    # Calculate hurdle return for each company
    for company, investment_info in company_investments.items():
        # Find the exit date for this company
        company_exit_info = exit_timeline[exit_timeline["Company"] == company]
        
        if not company_exit_info.empty:
            exit_date = company_exit_info.iloc[0]["Exit Date"]
            
            # Handle NaT (written-off companies) - use the latest date in data
            if pd.isna(exit_date) or exit_date is pd.NaT:
                latest_date = data_df["Date"].max()
                exit_date = pd.to_datetime(latest_date)
                exit_status = "Written Off (using latest date)"
            else:
                exit_date = pd.to_datetime(exit_date)
                exit_status = "Planned Exit"
        else:
            # Company not in exit timeline - use latest date in data
            latest_date = data_df["Date"].max()
            exit_date = pd.to_datetime(latest_date)
            exit_status = "Not in Exit Plan (using latest date)"
        
        # Handle multiple call dates for this company
        call_dates_list = investment_info['call_dates']
        call_amounts = investment_info['call_amounts']
        total_investment = investment_info['total_investment']
        
        if len(call_dates_list) == 1:
            # Single call date - use all investment for this date
            call_date = call_dates_list[0]
            investment_amount = call_amounts[0]
            
            if pd.notna(call_date) and pd.notna(exit_date):
                # Calculate days held from call date to exit date
                days_held = (exit_date - call_date).days
                days_held = max(0, days_held)  # Ensure non-negative
                
                # Calculate hurdle return for this investment using compound interest (matches Excel formula)
                years_held = days_held / 365
                hurdle_return = investment_amount * (pow(1 + hurdle_rate, years_held) - 1)
                
                breakdown_data.append({
                    'Company': company,
                    'Investment_Date': call_date.strftime('%Y-%m-%d'),
                    'Investment_Amount': investment_amount,
                    'Exit_Date': exit_date.strftime('%Y-%m-%d'),
                    'Days_Held': days_held,
                    'Years_Held': round(days_held / 365, 2),
                    'Hurdle_Rate': f"{hurdle_rate:.1%}",
                    'Hurdle_Return': hurdle_return,
                    'Exit_Status': exit_status,
                    'Call_Number': 1
                })
        else:
            # Multiple call dates - use actual investment amounts from Investment PR
            for call_num, (call_date, investment_amount) in enumerate(zip(call_dates_list, call_amounts), 1):
                if pd.notna(call_date) and pd.notna(exit_date):
                    # Calculate days held from this call date to exit date
                    days_held = (exit_date - call_date).days
                    days_held = max(0, days_held)  # Ensure non-negative
                    
                    # Calculate hurdle return for this specific call using compound interest (matches Excel formula)
                    years_held = days_held / 365
                    hurdle_return = investment_amount * (pow(1 + hurdle_rate, years_held) - 1)
                    
                    breakdown_data.append({
                        'Company': f"{company} (Call {call_num})",
                        'Investment_Date': call_date.strftime('%Y-%m-%d'),
                        'Investment_Amount': investment_amount,
                        'Exit_Date': exit_date.strftime('%Y-%m-%d'),
                        'Days_Held': days_held,
                        'Years_Held': round(days_held / 365, 2),
                        'Hurdle_Rate': f"{hurdle_rate:.1%}",
                        'Hurdle_Return': hurdle_return,
                        'Exit_Status': exit_status,
                        'Call_Number': call_num
                    })
    
    # Create DataFrame and sort by company and investment date
    breakdown_df = pd.DataFrame(breakdown_data)
    
    if not breakdown_df.empty:
        breakdown_df = breakdown_df.sort_values(['Company', 'Investment_Date'])
        
        # Add company totals (sum across all calls for multi-call companies)
        # Group by base company name (remove call number suffix)
        breakdown_df['Base_Company'] = breakdown_df['Company'].str.replace(r' \(Call \d+\)$', '', regex=True)
        
        company_totals = breakdown_df.groupby('Base_Company').agg({
            'Investment_Amount': 'sum',
            'Hurdle_Return': 'sum',
            'Days_Held': 'mean'  # Average days held for the company
        }).reset_index()
        
        company_totals['Company'] = company_totals['Base_Company']
        company_totals['Investment_Date'] = 'TOTAL'
        company_totals['Exit_Date'] = ''
        company_totals['Years_Held'] = round(company_totals['Days_Held'] / 365, 2)
        company_totals['Hurdle_Rate'] = f"{hurdle_rate:.1%}"
        company_totals['Exit_Status'] = 'Company Total'
        company_totals['Call_Number'] = 0
        
        # Reorder columns to match
        company_totals = company_totals[breakdown_df.columns]
        
        # Insert totals after each company's investments
        final_breakdown = []
        for base_company in breakdown_df['Base_Company'].unique():
            company_data = breakdown_df[breakdown_df['Base_Company'] == base_company]
            final_breakdown.append(company_data)
            
            company_total_row = company_totals[company_totals['Base_Company'] == base_company]
            final_breakdown.append(company_total_row)
        
        breakdown_df = pd.concat(final_breakdown, ignore_index=True)
        
        # Add grand total row at the bottom - only sum company totals to avoid double counting
        company_total_rows = breakdown_df[(breakdown_df['Investment_Date'] == 'TOTAL') & (breakdown_df['Company'] != 'FUND TOTAL')]
        grand_total_investment = company_total_rows['Investment_Amount'].sum()
        grand_total_hurdle = company_total_rows['Hurdle_Return'].sum()
        
        # Calculate weighted average years held for all investments (excluding company totals)
        individual_investments = breakdown_df[breakdown_df['Investment_Date'] != 'TOTAL']
        if not individual_investments.empty:
            weighted_avg_years = (individual_investments['Investment_Amount'] * individual_investments['Years_Held']).sum() / individual_investments['Investment_Amount'].sum()
        else:
            weighted_avg_years = 0
        
        # Add grand total row directly to avoid pandas FutureWarning about NA concatenation
        if grand_total_hurdle > 0:  # Only add if there's meaningful data
            # Create a copy of breakdown_df and add the row directly
            grand_total_data = {
                'Company': 'FUND TOTAL',
                'Investment_Date': 'TOTAL',  # Use 'TOTAL' instead of empty string
                'Investment_Amount': grand_total_investment,  # Use calculated total instead of 0
                'Exit_Date': '',
                'Days_Held': 0,  # Use 0 instead of NA
                'Years_Held': round(weighted_avg_years, 2),  # Use calculated weighted average
                'Hurdle_Rate': f"{hurdle_rate:.1%}",
                'Hurdle_Return': grand_total_hurdle,
                'Exit_Status': 'Fund Total',
                'Call_Number': 0,
                'Base_Company': 'FUND TOTAL'
            }
            
            # Convert to DataFrame with a single row and concatenate
            grand_total_df = pd.DataFrame([grand_total_data])
            breakdown_df = pd.concat([breakdown_df, grand_total_df], ignore_index=True)
        
        # Remove the helper column
        breakdown_df = breakdown_df.drop(columns=['Base_Company'])
    
    return breakdown_df
