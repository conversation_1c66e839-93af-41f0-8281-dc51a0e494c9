# ipo_scenario_phase2.py
# Enhanced IPO scenario display with Phase 2 features

import streamlit as st
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from ipo_scenario_advanced import (
    calculate_ipo_distributions_advanced,
    display_custom_percentage_editor,
    apply_fees_to_ipo_distributions,
    apply_enhanced_fees_to_ipo_distributions,
    create_ipo_visualization,
    validate_custom_percentages
)

def display_ipo_scenario_section_phase2(edited_df, company_net_investments, fee_tracker=None, total_fees=0):
    """
    Enhanced IPO scenario section with Phase 2 features.
    
    Args:
        edited_df: DataFrame with exit plan data
        company_net_investments: Dictionary of company net investments
        fee_tracker: Fee tracking information
        total_fees: Total management fees
    """
    st.markdown("### 📈 IPO Scenario Analysis")
    
    # IPO scenario toggle
    enable_ipo = st.checkbox("Enable IPO Scenario", key="enable_ipo_scenario", 
                            help="Model IPO exits with distributed payments over time")
    
    if enable_ipo:
        # Phase 2: Advanced configuration options
        st.markdown("#### IPO Distribution Configuration")
        
        config_col1, config_col2, config_col3, config_col4 = st.columns([1, 1, 1, 1])
        
        with config_col1:
            # Distribution frequency selector
            frequency = st.selectbox(
                "Distribution Frequency",
                options=['quarterly', 'semi-annual', 'annual', 'custom'],
                format_func=lambda x: {
                    'quarterly': 'Quarterly',
                    'semi-annual': 'Semi-Annual',
                    'annual': 'Annual',
                    'custom': 'Custom'
                }[x],
                index=1,  # Default to semi-annual
                key="ipo_distribution_frequency"
            )
        
        with config_col2:
            # Distribution period
            distribution_years = st.number_input(
                "Distribution Period (Years)",
                min_value=1,
                max_value=5,
                value=3,
                step=1,
                key="ipo_distribution_years"
            )
        
        with config_col3:
            # Distribution pattern selector
            pattern_options = ['equal', 'custom']
            pattern = st.selectbox(
                "Distribution Pattern",
                options=pattern_options,
                format_func=lambda x: {
                    'equal': 'Equal Distribution',
                    'custom': 'Custom Percentages'
                }[x],
                key="ipo_distribution_pattern"
            )
        
        with config_col4:
            # Calculate number of distributions
            if frequency == 'quarterly':
                num_distributions = distribution_years * 4
                months_between_distributions = 3
            elif frequency == 'annual':
                num_distributions = distribution_years * 1
                months_between_distributions = 12
            elif frequency == 'custom':
                # Default for custom frequency
                months_between_distributions = 4
                total_months = distribution_years * 12
                num_distributions = max(1, total_months // months_between_distributions)
            else:  # semi-annual
                num_distributions = distribution_years * 2
                months_between_distributions = 6
            
            st.metric("Total Distributions", num_distributions)
        
        # Custom frequency configuration (separate section for better UX)
        if frequency == 'custom':
            st.markdown("**Custom Frequency Configuration**")
            custom_col1, custom_col2 = st.columns([2, 2])
            
            with custom_col1:
                # Override the default value with user input
                months_between_distributions = st.number_input(
                    "Months Between Distributions",
                    min_value=1,
                    max_value=24,
                    value=months_between_distributions,  # Use the default as starting value
                    step=1,
                    key="ipo_custom_months_between",
                    help="Specify the number of months between each distribution"
                )
            
            with custom_col2:
                # Recalculate distributions with user input
                total_months = distribution_years * 12
                num_distributions = max(1, total_months // months_between_distributions)
                st.metric("Updated Total Distributions", num_distributions)
                
                # Show frequency description
                if months_between_distributions == 1:
                    freq_desc = "Monthly"
                elif months_between_distributions == 2:
                    freq_desc = "Bi-Monthly"
                elif months_between_distributions == 3:
                    freq_desc = "Quarterly"
                elif months_between_distributions == 6:
                    freq_desc = "Semi-Annual"
                elif months_between_distributions == 12:
                    freq_desc = "Annual"
                else:
                    freq_desc = f"Every {months_between_distributions} Months"
                
                st.caption(f"📅 **{freq_desc}** distributions")
        
        # Custom percentage editor if custom pattern selected
        custom_percentages = None
        if pattern == 'custom':
            st.divider()
            custom_percentages = display_custom_percentage_editor(
                num_distributions, 
                "ipo_scenario"
            )
            if custom_percentages is None:
                st.warning("⚠️ Please ensure percentages sum to 100% before proceeding")
                return None
        
        # Phase 2: Distribution configuration (removed fee allocation options)
        # Using decremental fee allocation by default (similar to waterfall method)
        
        # Initialize session state for IPO selections
        if 'ipo_selected_companies' not in st.session_state:
            st.session_state.ipo_selected_companies = set()
        
        # Create selection interface
        st.divider()
        st.markdown("#### Select Companies for IPO")
        
        # Filter out written-off companies (MOIC = 0)
        eligible_companies = edited_df[edited_df['MOIC'] > 0].copy()
        
        if eligible_companies.empty:
            st.warning("No eligible companies for IPO scenario (all companies have MOIC = 0)")
            return None
        
        # Quick selection options
        select_col1, select_col2, select_col3 = st.columns([1, 1, 2])
        with select_col1:
            if st.button("Select All", key="ipo_select_all"):
                st.session_state.ipo_selected_companies = set(eligible_companies['Company'].tolist())
                st.rerun()
        
        with select_col2:
            if st.button("Clear All", key="ipo_clear_all"):
                st.session_state.ipo_selected_companies = set()
                st.rerun()
        
        # Create columns for the selection table
        col_headers = st.columns([0.5, 3, 2, 2, 2])
        with col_headers[0]:
            st.write("**IPO**")
        with col_headers[1]:
            st.write("**Company**")
        with col_headers[2]:
            st.write("**Exit Value**")
        with col_headers[3]:
            st.write("**Exit Date**")
        with col_headers[4]:
            st.write("**Distributions**")
        
        # Track selected companies and their schedules
        selected_companies = []
        ipo_schedules = []
        
        # Display company selection rows
        for idx, row in eligible_companies.iterrows():
            cols = st.columns([0.5, 3, 2, 2, 2])
            
            company_name = row['Company']
            # Use Net Investment × MOIC for exit value
            net_investment = company_net_investments.get(company_name, 0)
            moic = row['MOIC']
            exit_value = row.get('Exit Value', net_investment * moic)
            exit_date = pd.to_datetime(row['Exit Date'])
            
            with cols[0]:
                # Checkbox for IPO selection
                is_selected = st.checkbox(
                    "",
                    key=f"ipo_select_{company_name}",
                    value=company_name in st.session_state.ipo_selected_companies
                )
                
                if is_selected:
                    selected_companies.append(company_name)
            
            with cols[1]:
                st.write(company_name)
            
            with cols[2]:
                st.write(f"${exit_value:,.0f}")
            
            with cols[3]:
                st.write(exit_date.strftime("%Y-%m-%d"))
            
            with cols[4]:
                if is_selected:
                    st.write(f"{num_distributions}")
                else:
                    st.write("-")
            
            # Calculate distribution schedule if selected
            if is_selected:
                # Calculate company-specific fees directly (bypassing potentially incorrect fee_tracker)
                company_fee_entry = None
                
                # Determine if this is a first exit (companies exiting on earliest date)
                earliest_exit_date = edited_df['Exit Date'].min()
                is_first_exit = (exit_date <= earliest_exit_date)
                
                # Get fee parameters from session state or defaults
                fee_balance = st.session_state.get('fund_fee_balance', 4000000)  # Default from typical fund
                fee_date = st.session_state.get('fund_fee_date', pd.to_datetime('2024-09-30'))
                q_fee = st.session_state.get('fund_quarterly_fee', 19194832.0)  # Sum of all company fees
                
                # Always calculate company-specific fees directly for IPO scenario
                company_fee_entry = calculate_company_specific_ipo_fees(
                    company_name, exit_date, fee_balance, fee_date, q_fee, is_first_exit
                )
                    

                
                # Get original investment amount for accurate fee calculations
                original_investment = company_net_investments.get(company_name, 0)
                
                schedule = calculate_ipo_distributions_advanced(
                    company_name, 
                    exit_value, 
                    exit_date,
                    distribution_years=distribution_years,
                    distribution_pattern=pattern,
                    custom_percentages=custom_percentages,
                    distribution_frequency=frequency,
                    custom_months_between=months_between_distributions if frequency == 'custom' else None,
                    original_investment=original_investment
                )
                
                # Apply enhanced fees to distributions using decremental method
                if company_fee_entry:
                    schedule = apply_enhanced_fees_to_ipo_distributions(
                        schedule, 
                        company_fee_entry, 
                        is_first_exit
                    )
                
                ipo_schedules.append(schedule)
        
        # Update session state
        st.session_state.ipo_selected_companies = set(selected_companies)
        st.session_state.ipo_scenario_changed = True
        st.session_state.ipo_schedules = ipo_schedules  # Store IPO schedules for details section
        
        # Display distribution preview if companies are selected
        if ipo_schedules:
            st.markdown("#### 📊 Distribution Schedule Preview")
            
            # Create summary metrics
            total_exit_value = sum(s['exit_value'] for s in ipo_schedules)
            num_companies = len(ipo_schedules)
            
            metric_cols = st.columns(4)
            with metric_cols[0]:
                st.metric("IPO Companies", num_companies)
            with metric_cols[1]:
                st.metric("Total Exit Value", f"${total_exit_value:,.0f}")
            with metric_cols[2]:
                st.metric("Distribution Period", f"{distribution_years} Years")
            with metric_cols[3]:
                st.metric("Frequency", frequency.replace('-', ' ').title())
            

            
            # Create distribution timeline table
            dist_df = create_ipo_distribution_df_advanced(ipo_schedules)
            
            # Show detailed distribution information
            with st.expander("View Detailed Distribution Information"):
                # Create detailed view with enhanced fee breakdown
                detailed_data = []
                for schedule in ipo_schedules:
                    company_name = schedule['company_name']
                    has_fees = 'distribution_amounts_after_fees' in schedule
                    is_first_exit = schedule.get('is_first_exit', False)
                    
                    for i, (date, gross_amt, pct) in enumerate(zip(
                        schedule['distribution_dates'],
                        schedule['distribution_amounts'],
                        schedule['distribution_percentages']
                    )):
                        if has_fees:
                            net_amt = schedule['distribution_amounts_after_fees'][i]
                            mgmt_fee = schedule.get('management_fee_amounts', [0] * len(schedule['distribution_dates']))[i]
                            cc_fee = schedule.get('capital_call_fee_amounts', [0] * len(schedule['distribution_dates']))[i]
                            pr_fee = schedule.get('priority_return_amounts', [0] * len(schedule['distribution_dates']))[i]
                            total_fee = mgmt_fee + cc_fee + pr_fee
                        else:
                            net_amt = gross_amt
                            mgmt_fee = cc_fee = pr_fee = total_fee = 0
                        
                        # Build fee detail string
                        fee_details = []
                        if mgmt_fee > 0:
                            fee_details.append(f"Mgmt: ${mgmt_fee:,.0f}")
                        if cc_fee > 0:
                            fee_details.append(f"CC: ${cc_fee:,.0f}")
                        if pr_fee > 0:
                            fee_details.append(f"PR: ${pr_fee:,.0f}")
                        
                        fee_detail_str = " | ".join(fee_details) if fee_details else "-"
                        
                        detailed_data.append({
                            'Company': company_name,
                            'Distribution #': i + 1,
                            'Date': date.strftime('%Y-%m-%d'),
                            'Gross Amount': f"${gross_amt:,.0f}",
                            'Management Fees': f"${mgmt_fee:,.0f}" if mgmt_fee > 0 else "-",
                            'Capital Call Fees': f"${cc_fee:,.0f}" if cc_fee > 0 else "-",
                            'Priority Return': f"${pr_fee:,.0f}" if pr_fee > 0 else "-",
                            'Total Fees': f"${total_fee:,.0f}" if total_fee > 0 else "-",
                            'Net Amount': f"${net_amt:,.0f}",
                            'Percentage': f"{pct:.1f}%",
                            'First Exit': "Yes" if is_first_exit else "No"
                        })
                
                detailed_df = pd.DataFrame(detailed_data)
                
                # Display fee summary if any fees exist
                total_mgmt_fees = sum([schedule.get('total_management_fees', 0) for schedule in ipo_schedules])
                total_cc_fees = sum([schedule.get('total_capital_call_fees', 0) for schedule in ipo_schedules])
                total_pr_fees = sum([schedule.get('total_priority_return', 0) for schedule in ipo_schedules])
                
                if total_mgmt_fees > 0 or total_cc_fees > 0 or total_pr_fees > 0:
                    st.markdown("**Fee Summary:**")
                    summary_cols = st.columns(4)
                    with summary_cols[0]:
                        st.metric("Management Fees", f"${total_mgmt_fees:,.0f}")
                    with summary_cols[1]:
                        st.metric("Capital Call Fees", f"${total_cc_fees:,.0f}")
                    with summary_cols[2]:
                        st.metric("Priority Return", f"${total_pr_fees:,.0f}")
                    with summary_cols[3]:
                        st.metric("Total Fees", f"${total_mgmt_fees + total_cc_fees + total_pr_fees:,.0f}")
                    
                    st.markdown("---")
                    st.caption("🔹 **Decremental Allocation**: Management fees accrue quarterly but decrease as shares are distributed over time")
                
                st.dataframe(detailed_df, use_container_width=True)
            
            return ipo_schedules
        else:
            st.info("👆 Select companies above to see distribution preview")
            # Clear IPO schedules from session state when no companies selected
            st.session_state.ipo_schedules = []
            return None

    # Clear IPO schedules from session state when IPO scenario is disabled
    st.session_state.ipo_schedules = []
    return None

def create_ipo_distribution_df_advanced(ipo_schedules):
    """
    Create a DataFrame with advanced IPO distribution details including fees.
    
    Args:
        ipo_schedules: List of IPO distribution schedules
    
    Returns:
        DataFrame with distribution details
    """
    rows = []
    for schedule in ipo_schedules:
        company_name = schedule['company_name']
        exit_value = schedule['exit_value']
        has_fees = 'distribution_amounts_after_fees' in schedule
        
        for i, (date, gross_amt, pct) in enumerate(zip(
            schedule['distribution_dates'],
            schedule['distribution_amounts'],
            schedule['distribution_percentages']
        )):
            if has_fees:
                net_amt = schedule['distribution_amounts_after_fees'][i]
                fee_amt = schedule['fee_amounts'][i]
            else:
                net_amt = gross_amt
                fee_amt = 0
            
            rows.append({
                'Company': company_name,
                'Distribution #': i + 1,
                'Date': date,
                'Gross Amount': gross_amt,
                'Fee Amount': fee_amt,
                'Net Amount': net_amt,
                'Percentage': pct,
                'Exit Value': exit_value
            })
    
    return pd.DataFrame(rows)

# Maintain backward compatibility
def create_ipo_distribution_df(ipo_schedules):
    """Legacy function for backward compatibility."""
    return create_ipo_distribution_df_advanced(ipo_schedules)

def calculate_company_specific_ipo_fees(company_name, exit_date, fee_balance, fee_date, q_fee, is_first_exit):
    """
    Calculate company-specific management fees for IPO scenario.
    Each company gets its proportional share of management fees.
    
    Args:
        company_name: Name of the company
        exit_date: Exit date for the company
        fee_balance: Current fee balance
        fee_date: Fee balance date
        q_fee: Quarterly fee amount
        is_first_exit: Whether this is the first exit
    
    Returns:
        Dictionary with detailed fee breakdown
    """
    import pandas as pd
    from financial_calculations import calculate_company_management_fees_with_hurdle
    from data_loader import load_company_management_fees, get_company_quarterly_fee
    
    # Convert dates to pandas Timestamps to ensure compatibility
    exit_date = pd.to_datetime(exit_date)
    fee_date = pd.to_datetime(fee_date)
    
    # Load company-specific fee mapping
    company_fees, fee_mapping = load_company_management_fees()
    
    # Calculate company-specific fees
    fee_details = calculate_company_management_fees_with_hurdle(
        fee_date, 
        exit_date, 
        fee_balance, 
        company_name,
        company_fees=company_fees,
        fee_mapping=fee_mapping,
        hurdle_rate=0.08,
        include_capital_calls=True,  # Include capital call fees
        is_first_exit=is_first_exit
    )
    
    # Extract individual fee components
    management_fees = fee_details.get('mgmt_fees_only', 0)
    capital_call_fees = fee_details.get('capital_call_fees', 0)
    capital_call_pr = fee_details.get('capital_call_pr', 0)
    priority_return = capital_call_pr  # Priority return comes from capital calls
    
    return {
        'Management Fees': management_fees,
        'Capital Call Fees': capital_call_fees,
        'Priority Return': priority_return,
        'Total Fees': fee_details.get('total_fees', 0),
        'Fee Basis': fee_details.get('fee_basis', 0),
        'New Fees': fee_details.get('new_fees', 0),
        'Days': fee_details.get('days', 0),
        'Company Quarterly Fee': fee_details.get('company_quarterly_fee', 0),
        'Company Proportion': fee_details.get('company_proportion', 0),
        'Is First Exit': is_first_exit
    }
