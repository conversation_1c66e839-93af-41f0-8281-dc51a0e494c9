import pandas as pd
import streamlit as st
import numpy as np
import os # Import os module
from typing import Dict, Tuple, Optional

# Company-specific quarterly management fees from "Mgmt Fee x Investment Calc" tab
COMPANY_QUARTERLY_FEES = {
    'Gainwell': 4310884.00,
    'Peraton': 5841291.00,
    'Cubic': 2817633.00,
    'Finalsite': 1991663.25,
    'HMH': 4233361.75
}

def create_company_name_mapping() -> Dict[str, str]:
    """
    Create mapping from Deal Names (in cash flows) to company fee names.
    This handles variations in company names between different data sources.
    """
    return {
        # Direct mappings
        'Gainwell': 'Gainwell',
        'Peraton': 'Peraton',
        'Cubic': 'Cubic',
        'Finalsite': 'Finalsite',
        'HMH': 'HMH',
        
        # Full legal entity names to fee names
        'Gainwell Topco Holdings L.P.': 'Gainwell',
        'Peraton Holdings II L.P.': 'Peraton',
        'Atlas Topco Holdings L.P.': 'Cubic',
        'Cubic Corporation': 'Cubic',
        'Finalsite Holdings, Inc': 'Finalsite',
        'Finalsite Holdings, Inc.': 'Finalsite',
        'Harbor Topco Holdings L.P.': 'HMH',
        'HMH Topco Holdings L.P.': 'HMH',
        'HMH Topco Holdings L.P. (NWEA)': 'HMH',
        'NWEA': 'HMH',
        
        # Additional variations that might appear in data
        'Gainwell Technologies': 'Gainwell',
        'Gainwell Technologies, LLC': 'Gainwell',
        'Peraton Inc': 'Peraton',
        'Peraton Inc.': 'Peraton',
        'Peraton Corp.': 'Peraton',
        'Finalsite Inc': 'Finalsite',
        'Finalsite Inc.': 'Finalsite',
        'Houghton Mifflin Harcourt': 'HMH',
        'HMH Holdings': 'HMH',
        'HMH Company': 'HMH'
    }

def load_company_management_fees() -> Tuple[Dict[str, float], Dict[str, str]]:
    """
    Load company-specific management fees from the Excel file.
    
    Returns:
        Tuple of (company_fees_dict, company_name_mapping)
    """
    try:
        # Try to load from Fund VII Scenario Analysis file
        script_dir = os.path.dirname(os.path.abspath(__file__)) # Get the absolute directory of the script
        excel_file_path = os.path.join(script_dir, "2025.03.31 Fund VII Scenario Analysis v2.xlsx") # Construct absolute path
        
        # Check if file exists
        if not os.path.exists(excel_file_path):
            st.warning(f"Excel file not found at: {excel_file_path}")
            return get_default_company_fees()
            
        df_mgmt_fee_calc = pd.read_excel(
            excel_file_path, 
            sheet_name="Mgmt Fee x Investment Calc",
            header=None  # We'll handle headers manually due to complex structure
        )
        
        # Find the Grand Total row
        total_row_idx = None
        for idx, row in df_mgmt_fee_calc.iterrows():
            if row.astype(str).str.contains("Grand Total", case=False, na=False).any():
                total_row_idx = idx
                break
        
        if total_row_idx is None:
            st.warning("Could not find Grand Total row in management fee calculations")
            return get_default_company_fees()
        
        total_row = df_mgmt_fee_calc.iloc[total_row_idx]
        
        # Extract company fees from the total row based on known column positions
        # From our analysis: Gainwell(8), Peraton(11), Cubic(14), Finalsite(17), HMH(20)
        company_fees = {}
        fee_columns = {
            'Gainwell': 8,
            'Peraton': 11,
            'Cubic': 14,
            'Finalsite': 17,
            'HMH': 20
        }
        
        for company, col_idx in fee_columns.items():
            try:
                if col_idx < len(total_row):
                    fee_str = str(total_row.iloc[col_idx]).strip()
                    # Clean and convert to float
                    fee_value = float(fee_str.replace(',', '').replace('$', '').replace(' ', ''))
                    company_fees[company] = fee_value
                else:
                    st.warning(f"Column {col_idx} not found for {company}")
                    company_fees[company] = COMPANY_QUARTERLY_FEES[company]
            except (ValueError, AttributeError) as e:
                st.warning(f"Could not parse fee for {company}: {e}")
                company_fees[company] = COMPANY_QUARTERLY_FEES[company]
        
        # Create mapping from Deal Names to fee company names
        fee_mapping = create_company_name_mapping()
        
        # Validate that we got reasonable values
        total_fees = sum(company_fees.values())
        expected_total = 19194832.0  # From app.py
        
        if abs(total_fees - expected_total) / expected_total > 0.05:  # 5% tolerance
            st.warning(f"Extracted fees total (${total_fees:,.0f}) differs from expected (${expected_total:,.0f})")
        
        return company_fees, fee_mapping
        
    except Exception as e:
        st.warning(f"Could not load company fees from Excel: {str(e)}")
        return get_default_company_fees()

def get_default_company_fees() -> Tuple[Dict[str, float], Dict[str, str]]:
    """
    Return default company fees if Excel loading fails.
    """
    mapping = create_company_name_mapping()
    return COMPANY_QUARTERLY_FEES.copy(), mapping

def get_company_quarterly_fee(company_name: str, company_fees: Dict[str, float] = None, fee_mapping: Dict[str, str] = None) -> float:
    """
    Get the quarterly management fee for a specific company.
    
    Args:
        company_name: Name of the company (Deal Name from cash flows)
        company_fees: Dictionary of company fees (optional, will load if not provided)
        fee_mapping: Dictionary mapping deal names to fee company names (optional)
        
    Returns:
        Quarterly fee amount for the company
    """
    if company_fees is None or fee_mapping is None:
        company_fees, fee_mapping = load_company_management_fees()
    
    # Try direct lookup first
    if company_name in company_fees:
        return company_fees[company_name]
    
    # Try mapped lookup
    mapped_name = fee_mapping.get(company_name)
    if mapped_name and mapped_name in company_fees:
        return company_fees[mapped_name]
    
    # If no mapping found, calculate proportional fee based on total
    # This is a fallback - ideally all companies should be mapped
    total_fund_quarterly_fee = sum(company_fees.values()) if company_fees else 19194832.0
    num_companies = len(company_fees) if company_fees else 5
    fallback_fee = total_fund_quarterly_fee / num_companies
    
    # st.warning(f"No specific fee mapping found for {company_name}. Using average fee: ${fallback_fee:,.2f}")
    return fallback_fee

# Cache with dependency on call dates to ensure fresh data when Investment PR amounts change
@st.cache_data
def load_data():
    """
    Enhanced data loader that includes company-specific management fees and call dates
    """
    try:
        # Load Gross Cash Flows (for existing script logic)
        df_gross_flows = pd.read_excel("Track Record Cashflows - IR version.xlsx", sheet_name="Gross Cash Flows", skiprows=1)
        df_gross_flows.columns = ["Index", "Fund Name", "Deal Name", "Realized/Unrealized", "Date", "Contributions", "Distributions", "Book Value"]
        df_gross_flows = df_gross_flows.dropna(subset=["Deal Name", "Date"])
        df_gross_flows["Date"] = pd.to_datetime(df_gross_flows["Date"])
        for col in ["Contributions", "Distributions", "Book Value"]:
            df_gross_flows[col] = pd.to_numeric(df_gross_flows[col], errors="coerce").fillna(0)
        df_gross_flows["Deal Name"] = df_gross_flows["Deal Name"].astype(str).str.strip()

        # Load Net Cash Flows
        df_net_cash_flows = pd.read_excel("Track Record Cashflows - IR version.xlsx", sheet_name="Net Cash Flows", skiprows=1)
        df_net_cash_flows.columns = ["Index", "Fund Name", "Deal Name", "Realized/Unrealized", "Date", "Contributions", "Distributions", "Book Value"]
        df_net_cash_flows = df_net_cash_flows.dropna(subset=["Deal Name", "Date"])
        df_net_cash_flows["Date"] = pd.to_datetime(df_net_cash_flows["Date"])
        for col in ["Contributions", "Distributions", "Book Value"]:
            df_net_cash_flows[col] = pd.to_numeric(df_net_cash_flows[col], errors="coerce").fillna(0)
        df_net_cash_flows["Deal Name"] = df_net_cash_flows["Deal Name"].astype(str).str.strip()

        # Load net book value
        df_terminal_book_values = pd.read_excel("Track Record Cashflows - IR version.xlsx", sheet_name="net book value")
        df_terminal_book_values.columns = ["Portfolio_Company_NBV", "Date_NBV", "Book_Value_NBV"]
        df_terminal_book_values["Date_NBV"] = pd.to_datetime(df_terminal_book_values["Date_NBV"])
        df_terminal_book_values["Book_Value_NBV"] = df_terminal_book_values["Book_Value_NBV"].replace('-', 0)
        df_terminal_book_values["Book_Value_NBV"] = pd.to_numeric(df_terminal_book_values["Book_Value_NBV"], errors="coerce").fillna(0)
        df_terminal_book_values["Portfolio_Company_NBV"] = df_terminal_book_values["Portfolio_Company_NBV"].astype(str).str.strip()

        # Load company-specific management fees
        company_fees, fee_mapping = load_company_management_fees()
        
        # Load call dates from Investment PR tab
        call_dates = load_call_dates_from_investment_pr()
        
        # Load Investment PR amounts (new)
        investment_pr_amounts = get_investment_pr_amounts()
        
        return {
            "gross_flows": df_gross_flows,
            "net_cash_flows": df_net_cash_flows,
            "terminal_book_values": df_terminal_book_values,
            "company_fees": company_fees,
            "fee_mapping": fee_mapping,
            "call_dates": call_dates,
            "investment_pr_amounts": investment_pr_amounts
        }
        
    except Exception as e:
        st.error(f"Error loading data: {str(e)}")
        # Return basic structure with default company fees if Excel loading fails
        default_fees, default_mapping = get_default_company_fees()
        return {
            "gross_flows": pd.DataFrame(),
            "net_cash_flows": pd.DataFrame(),
            "terminal_book_values": pd.DataFrame(),
            "company_fees": default_fees,
            "fee_mapping": default_mapping,
            "call_dates": {},
            "investment_pr_amounts": {}
        }

def validate_company_fees(company_fees: Dict[str, float]) -> bool:
    """
    Validate that the company fees are reasonable.
    """
    if not company_fees:
        return False
    
    total_fees = sum(company_fees.values())
    expected_total = 19194832.0
    
    # Check if total is within 10% of expected
    if abs(total_fees - expected_total) / expected_total > 0.1:
        return False
    
    # Check if any individual fee is unreasonably high or low
    for company, fee in company_fees.items():
        if fee <= 0 or fee > total_fees * 0.5:
            return False
    
    return True

def load_call_dates_from_investment_pr():
    """
    Load call dates and investment amounts from the Investment PR tab in the scenario analysis file.
    Uses the Non-Affiliated LP row (row 221) for accurate investment amounts.
    Returns a dictionary mapping company names to their call information.
    For companies with multiple call dates, returns a list of all dates with corresponding amounts.
    """
    try:
        excel_file = "2025.03.31 Fund VII Scenario Analysis v2.xlsx"
        if not os.path.exists(excel_file):
            st.warning(f"Scenario analysis file not found: {excel_file}")
            return {}
        
        # Load without header to see raw structure
        df = pd.read_excel(excel_file, sheet_name="Investment PR", header=None)
        
        # Row 4 contains company names, Row 10 contains dates
        company_row = df.iloc[4]
        dates_row = df.iloc[10]
        
        # Find the Non-Affiliated LP's row (row 221 based on analysis)
        non_affiliated_row_idx = None
        for row_idx in range(df.shape[0]):
            for col_idx in range(min(5, df.shape[1])):
                cell_value = df.iloc[row_idx, col_idx]
                if isinstance(cell_value, str) and "Non-Affiliated LP" in cell_value:
                    non_affiliated_row_idx = row_idx
                    break
            if non_affiliated_row_idx is not None:
                break
        
        if non_affiliated_row_idx is None:
            st.warning("Could not find 'Non-Affiliated LP's' row in Investment PR tab")
            return {}
        
        # Get the Non-Affiliated LP's row with investment amounts
        non_affiliated_row = df.iloc[non_affiliated_row_idx]
        
        # Extract company names from row 4
        companies = {}
        for col_idx, company in enumerate(company_row):
            if pd.notna(company) and isinstance(company, str) and company.strip():
                companies[col_idx] = company.strip()
        
        # Extract dates from row 10
        dates = {}
        for col_idx, date_val in enumerate(dates_row):
            if pd.notna(date_val):
                try:
                    parsed_date = pd.to_datetime(date_val)
                    dates[col_idx] = parsed_date
                except:
                    pass
        
        # Extract investment amounts from Non-Affiliated LP's row
        investment_amounts = {}
        for col_idx, amount_val in enumerate(non_affiliated_row):
            if pd.notna(amount_val) and isinstance(amount_val, (int, float)) and amount_val > 0:
                investment_amounts[col_idx] = amount_val
        
        # Create call information mapping - collect ALL dates and amounts for each company
        call_info_by_company = {}
        for col_idx in companies:
            company_name = companies[col_idx]
            call_date = dates.get(col_idx)
            investment_amount = investment_amounts.get(col_idx)
            
            if call_date is not None and investment_amount is not None:
                if company_name not in call_info_by_company:
                    call_info_by_company[company_name] = []
                
                call_info_by_company[company_name].append({
                    'call_date': call_date,
                    'investment_amount': investment_amount
                })
        
        # Create mapping from cash flow names to Investment PR names
        cash_flow_to_pr_mapping = {
            'Gainwell Technologies, LLC': 'Gainwell',
            'Peraton Corp.': 'Peraton',
            'Cubic Corporation': 'Cubic',
            'Finalsite Holdings, Inc': 'Finalsite',
            'HMH Company': 'HMH',
            'Coronis Health, LLC': 'Coronis'
        }
        
        # Map call information to cash flow company names - keep ALL dates and amounts
        final_call_info = {}
        for cash_flow_name, pr_name in cash_flow_to_pr_mapping.items():
            if pr_name in call_info_by_company:
                # Sort by date chronologically and keep all of them
                all_calls = sorted(call_info_by_company[pr_name], key=lambda x: x['call_date'])
                
                # If only one call, store as single dict (for backward compatibility with dates)
                # If multiple calls, store as list
                if len(all_calls) == 1:
                    # For backward compatibility, also store just the date
                    final_call_info[cash_flow_name] = all_calls[0]['call_date']
                    # Store the full call info under a new key
                    final_call_info[f"{cash_flow_name}_call_info"] = all_calls[0]
                else:
                    # For multiple calls, store both the dates list and full call info
                    final_call_info[cash_flow_name] = [call['call_date'] for call in all_calls]
                    final_call_info[f"{cash_flow_name}_call_info"] = all_calls
        
        return final_call_info
        
    except Exception as e:
        st.warning(f"Error loading call dates from Investment PR tab: {str(e)}")
        return {}

def get_investment_pr_amounts():
    """
    Get investment amounts directly from Investment PR tab (Non-Affiliated LP row).
    Returns a dictionary mapping cash flow company names to their total investment amounts.
    This replaces the calculation from cash flows to use the Excel data directly.
    """
    try:
        excel_file = "2025.03.31 Fund VII Scenario Analysis v2.xlsx"
        if not os.path.exists(excel_file):
            return {}
        
        # Load Investment PR tab
        df = pd.read_excel(excel_file, sheet_name="Investment PR", header=None)
        
        # Row 4 contains company names
        company_row = df.iloc[4]
        
        # Find the Non-Affiliated LP's row
        non_affiliated_row_idx = None
        for row_idx in range(df.shape[0]):
            for col_idx in range(min(5, df.shape[1])):
                cell_value = df.iloc[row_idx, col_idx]
                if isinstance(cell_value, str) and "Non-Affiliated LP" in cell_value:
                    non_affiliated_row_idx = row_idx
                    break
            if non_affiliated_row_idx is not None:
                break
        
        if non_affiliated_row_idx is None:
            return {}
        
        # Get the Non-Affiliated LP's row with investment amounts
        non_affiliated_row = df.iloc[non_affiliated_row_idx]
        
        # Extract company names from row 4
        companies = {}
        for col_idx, company in enumerate(company_row):
            if pd.notna(company) and isinstance(company, str) and company.strip():
                companies[col_idx] = company.strip()
        
        # Extract investment amounts from Non-Affiliated LP's row
        investment_amounts = {}
        for col_idx, amount_val in enumerate(non_affiliated_row):
            if pd.notna(amount_val) and isinstance(amount_val, (int, float)) and amount_val > 0:
                investment_amounts[col_idx] = amount_val
        
        # For companies with multiple columns (multiple capital calls), sum them
        company_totals = {}
        for col_idx in companies:
            company_name = companies[col_idx]
            amount = investment_amounts.get(col_idx, 0)
            
            if company_name in company_totals:
                company_totals[company_name] += amount
            else:
                company_totals[company_name] = amount
        
        # Create mapping from Investment PR names to cash flow names
        pr_to_cash_flow_mapping = {
            'Gainwell': 'Gainwell Technologies, LLC',
            'Peraton': 'Peraton Corp.',
            'Cubic': 'Cubic Corporation',
            'Finalsite': 'Finalsite Holdings, Inc',
            'HMH': 'HMH Company',
            'Coronis': 'Coronis Health, LLC'
        }
        
        # Map to cash flow company names
        final_amounts = {}
        for pr_name, total_amount in company_totals.items():
            cash_flow_name = pr_to_cash_flow_mapping.get(pr_name)
            if cash_flow_name:
                final_amounts[cash_flow_name] = total_amount
        
        return final_amounts
        
    except Exception as e:
        st.warning(f"Error loading Investment PR amounts: {str(e)}")
        return {}