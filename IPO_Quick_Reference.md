# IPO Scenario Feature - Quick Reference Card

## 🚀 IPO Scenario Feature Overview
Model IPO exits with distributed payments over multiple years, complete with advanced analytics.

## ✨ Features Available (Phases 1-3 Complete)

### 📋 Basic IPO Modeling (Phase 1)
- ☑️ Select companies for IPO scenario
- ☑️ Bi-yearly distributions over 3 years
- ☑️ Distribution schedule preview
- ☑️ Impact on fund IRR/MOIC

### 🎯 Advanced Configuration (Phase 2)
- ☑️ **Distribution Frequency**
  - Quarterly (4x/year)
  - Semi-Annual (2x/year)
  - Annual (1x/year)
  
- ☑️ **Distribution Patterns**
  - Equal: Same percentage each period
  - Front-Loaded: Higher early distributions
  - Back-Loaded: Higher later distributions
  - Custom: Define your own percentages
  
- ☑️ **Fee Allocation**
  - First Distribution: All fees upfront
  - Pro-Rata: Spread across distributions
  - Realized: With each distribution
  
- ☑️ **Flexible Period**: 1-5 years

### 📊 Analytics & Visualization (Phase 3)
- ☑️ **Scenario Comparison**
  - Standard Exit vs IPO metrics
  - IRR impact analysis
  - MOIC comparison
  
- ☑️ **Interactive Charts**
  - 📅 Distribution Timeline (Gantt)
  - 💰 Cash Flow Comparison
  - 📈 DPI Progression
  - 🔄 Time-Weighted Returns
  
- ☑️ **Key Insights**
  - DPI milestones tracking
  - Distribution period analysis
  - Company-specific returns

## 🎮 How to Use

### 1. Enable IPO Scenario
```
Fund Analysis Tab → IPO Scenario Analysis → ☑️ Enable IPO Scenario
```

### 2. Configure Settings
```
Distribution Frequency: [Semi-Annual ▼]
Distribution Period: [3 Years]
Distribution Pattern: [Equal ▼]
Fee Allocation: [First Distribution ▼]
```

### 3. Select Companies
```
☑️ ABC Corp     $100M    2026-12-31    6 distributions
☑️ XYZ Inc      $200M    2027-06-30    6 distributions
☐ DEF Ltd       $150M    2027-03-31    -
```

### 4. View Analytics
```
[📅 Timeline] [💰 Cash Flow] [📈 DPI] [🔄 Returns] [📊 Summary]
```

## 📈 Metrics Provided

| Metric | Standard Exit | IPO Scenario | Impact |
|--------|--------------|--------------|---------|
| IRR | 32.5% | 22.0% | -10.5% |
| MOIC | 2.5x | 2.5x | 0.0x |
| First Distribution | Immediate | +6 months | Delayed |
| Full Liquidity | Immediate | +3 years | Extended |

## 💡 Use Cases

### Scenario Planning
- Model realistic IPO exits
- Compare distribution strategies
- Optimize for IRR vs liquidity

### LP Communication
- Show distribution timeline
- Explain IRR impact
- Professional visualizations

### Risk Assessment
- Understand timing risks
- Plan capital needs
- Evaluate trade-offs

## 🔑 Key Benefits

1. **Accurate Modeling**: Reflects real-world IPO dynamics
2. **Flexibility**: Multiple patterns and frequencies
3. **Transparency**: Clear impact visualization
4. **Professional**: Export-ready analytics
5. **Comprehensive**: Full scenario analysis

## 📝 Tips

- **Front-loaded** distributions minimize IRR impact
- **Quarterly** distributions provide more liquidity events
- **Pro-rata** fees smooth expense recognition
- Use **DPI tracking** to monitor capital return

## 🛠️ Technical Details

- Integrates seamlessly with existing IRR model
- Session state management for performance
- Plotly visualizations for interactivity
- Excel/PDF export ready (Phase 4 coming)

## 📞 Support

For questions or issues:
- Check documentation files
- Review test scripts for examples
- Examine integration guides

---

**Current Status**: Phases 1-3 Complete ✅
**Next**: Phase 4 (Enhanced Export & Reporting) 🚧