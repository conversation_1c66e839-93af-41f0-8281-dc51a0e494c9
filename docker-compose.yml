services:
  streamlit-app:
    build: .
    ports:
      - "8502:8502"
    environment:
      - DOCKER_ENV=true
    volumes:
      # Mount the Excel file
      - ./Track Record Cashflows - IR version.xlsx:/app/Track Record Cashflows - IR version.xlsx:ro
      # Mount the logo
      - ./Veritas Logo.jpg:/app/Veritas Logo.jpg:ro
      # Optionally mount the templates database if you want persistence
      - ./templates.db:/app/templates.db
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8502/_stcore/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s