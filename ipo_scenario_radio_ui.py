# ipo_scenario_radio_ui.py
# Alternative IPO scenario UI using radio buttons instead of selectboxes

import streamlit as st
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from ipo_scenario_advanced import (
    calculate_ipo_distributions_advanced,
    display_custom_percentage_editor,
    apply_enhanced_fees_to_ipo_distributions,
    validate_custom_percentages
)
from ipo_scenario_phase2 import (
    calculate_company_specific_ipo_fees
)

def display_ipo_scenario_section_radio_ui(edited_df, company_net_investments, fee_tracker=None, total_fees=0):
    """
    Alternative IPO scenario section using radio buttons instead of selectboxes.
    
    Args:
        edited_df: DataFrame with exit plan data
        company_net_investments: Dictionary of company net investments
        fee_tracker: Fee tracking information
        total_fees: Total management fees
    """
    st.markdown("### 📈 IPO Scenario Analysis")
    
    # IPO scenario toggle
    enable_ipo = st.checkbox("Enable IPO Scenario", key="enable_ipo_scenario", 
                            help="Model IPO exits with distributed payments over time")
    
    if enable_ipo:
        st.markdown("#### IPO Distribution Configuration")
        
        config_col1, config_col2, config_col3, config_col4 = st.columns([1, 1, 1, 1])
        
        with config_col1:
            # Distribution frequency using radio buttons
            st.markdown("**Distribution Frequency**")
            frequency_options = {
                'quarterly': 'Quarterly',
                'semi-annual': 'Semi-Annual', 
                'annual': 'Annual',
                'custom': 'Custom'
            }
            
            frequency = st.radio(
                "Frequency Selection",
                options=list(frequency_options.keys()),
                format_func=lambda x: frequency_options[x],
                index=1,  # Default to semi-annual
                key="ipo_distribution_frequency_radio",
                label_visibility="collapsed"
            )
        
        with config_col2:
            # Distribution period
            distribution_years = st.number_input(
                "Distribution Period (Years)",
                min_value=1,
                max_value=5,
                value=3,
                step=1,
                key="ipo_distribution_years"
            )
        
        with config_col3:
            # Distribution pattern using radio buttons
            st.markdown("**Distribution Pattern**")
            pattern_options = {
                'equal': 'Equal Distribution',
                'custom': 'Custom Percentages'
            }
            
            pattern = st.radio(
                "Pattern Selection",
                options=list(pattern_options.keys()),
                format_func=lambda x: pattern_options[x],
                key="ipo_distribution_pattern_radio",
                label_visibility="collapsed"
            )
        
        with config_col4:
            # Calculate number of distributions
            if frequency == 'quarterly':
                num_distributions = distribution_years * 4
                months_between_distributions = 3
            elif frequency == 'annual':
                num_distributions = distribution_years * 1
                months_between_distributions = 12
            elif frequency == 'custom':
                months_between_distributions = 4
                total_months = distribution_years * 12
                num_distributions = max(1, total_months // months_between_distributions)
            else:  # semi-annual
                num_distributions = distribution_years * 2
                months_between_distributions = 6
            
            st.metric("Total Distributions", num_distributions)
        
        # Custom frequency configuration (separate section for better UX)
        if frequency == 'custom':
            st.markdown("**Custom Frequency Configuration**")
            custom_col1, custom_col2 = st.columns([2, 2])
            
            with custom_col1:
                # Override the default value with user input
                months_between_distributions = st.number_input(
                    "Months Between Distributions",
                    min_value=1,
                    max_value=24,
                    value=months_between_distributions,  # Use the default as starting value
                    step=1,
                    key="ipo_custom_months_between",
                    help="Specify the number of months between each distribution"
                )
            
            with custom_col2:
                # Recalculate distributions with user input
                total_months = distribution_years * 12
                num_distributions = max(1, total_months // months_between_distributions)
                st.metric("Updated Total Distributions", num_distributions)
                
                # Show frequency description
                if months_between_distributions == 1:
                    freq_desc = "Monthly"
                elif months_between_distributions == 2:
                    freq_desc = "Bi-Monthly"
                elif months_between_distributions == 3:
                    freq_desc = "Quarterly"
                elif months_between_distributions == 6:
                    freq_desc = "Semi-Annual"
                elif months_between_distributions == 12:
                    freq_desc = "Annual"
                else:
                    freq_desc = f"Every {months_between_distributions} Months"
                
                st.caption(f"📅 **{freq_desc}** distributions")
        
        # Custom percentage editor if custom pattern selected
        custom_percentages = None
        if pattern == 'custom':
            st.divider()
            custom_percentages = display_custom_percentage_editor(
                num_distributions, 
                "ipo_scenario"
            )
            if custom_percentages is None:
                st.warning("⚠️ Please ensure percentages sum to 100% before proceeding")
                return None
        
        # Phase 2: Distribution configuration (removed fee allocation options)
        # Using decremental fee allocation by default (similar to waterfall method)
        
        # Initialize session state for IPO selections
        if 'ipo_selected_companies' not in st.session_state:
            st.session_state.ipo_selected_companies = set()
        
        # Create selection interface
        st.divider()
        st.markdown("#### Select Companies for IPO")
        
        # Filter out written-off companies (MOIC = 0)
        eligible_companies = edited_df[edited_df['MOIC'] > 0].copy()
        
        if eligible_companies.empty:
            st.warning("No eligible companies for IPO scenario (all companies have MOIC = 0)")
            return None
        
        # Quick selection options
        select_col1, select_col2, select_col3 = st.columns([1, 1, 2])
        with select_col1:
            if st.button("Select All", key="ipo_select_all"):
                st.session_state.ipo_selected_companies = set(eligible_companies['Company'].tolist())
                st.rerun()
        
        with select_col2:
            if st.button("Clear All", key="ipo_clear_all"):
                st.session_state.ipo_selected_companies = set()
                st.rerun()
        
        # Create columns for the selection table
        col_headers = st.columns([0.5, 2.5, 1.5, 2, 2, 1.5])
        with col_headers[0]:
            st.write("**IPO**")
        with col_headers[1]:
            st.write("**Company**")
        with col_headers[2]:
            st.write("**Exit Value**")
        with col_headers[3]:
            st.write("**First Sale Date**")
        with col_headers[4]:
            st.write("**Final Exit Date**")
        with col_headers[5]:
            st.write("**Distributions**")
        
        # Track selected companies and their schedules
        selected_companies = []
        ipo_schedules = []
        
        # Display company selection rows
        for idx, row in eligible_companies.iterrows():
            cols = st.columns([0.5, 2.5, 1.5, 2, 2, 1.5])
            
            company_name = row['Company']
            # Use Net Investment × MOIC for exit value
            net_investment = company_net_investments.get(company_name, 0)
            moic = row['MOIC']
            exit_value = row.get('Exit Value', net_investment * moic)
            exit_date = pd.to_datetime(row['Exit Date'])
            
            with cols[0]:
                # Checkbox for IPO selection
                is_selected = st.checkbox(
                    "",
                    key=f"ipo_select_{company_name}",
                    value=company_name in st.session_state.ipo_selected_companies,
                    label_visibility="collapsed"
                )
                
                if is_selected:
                    selected_companies.append(company_name)
            
            with cols[1]:
                st.write(company_name)
            
            with cols[2]:
                st.write(f"${exit_value:,.0f}")
            
            # Initialize first sale date and final exit date
            first_sale_date = None
            final_exit_date = exit_date
            
            with cols[3]:
                if is_selected:
                    # Calculate default first sale date (e.g., distribution_years before exit)
                    default_first_sale = exit_date - relativedelta(years=distribution_years)
                    
                    first_sale_date_str = st.text_input(
                        "First Sale",
                        value=default_first_sale.date().strftime("%Y-%m-%d"),
                        key=f"first_sale_{company_name}",
                        label_visibility="collapsed",
                        help="When the first IPO distribution occurs (YYYY-MM-DD format)"
                    )
                    
                    # Parse the date string
                    try:
                        first_sale_date = datetime.strptime(first_sale_date_str, "%Y-%m-%d").date()
                    except ValueError:
                        first_sale_date = default_first_sale.date()
                        if first_sale_date_str != default_first_sale.date().strftime("%Y-%m-%d"):
                            st.warning(f"Invalid first sale date format for {company_name}. Using default.")
                else:
                    st.write("-")
                    first_sale_date = None  # Define first_sale_date for non-selected companies
            
            with cols[4]:
                if is_selected:
                    # Allow user to modify final exit date
                    final_exit_date_str = st.text_input(
                        "Final Exit",
                        value=exit_date.date().strftime("%Y-%m-%d"),
                        key=f"final_exit_{company_name}",
                        label_visibility="collapsed",
                        help="When the last distribution occurs (YYYY-MM-DD format)"
                    )
                    
                    # Parse the date string
                    try:
                        final_exit_date = datetime.strptime(final_exit_date_str, "%Y-%m-%d").date()
                    except ValueError:
                        final_exit_date = exit_date.date()
                        if final_exit_date_str != exit_date.date().strftime("%Y-%m-%d"):
                            st.warning(f"Invalid final exit date format for {company_name}. Using default.")
                else:
                    st.write(exit_date.strftime("%Y-%m-%d"))
                    final_exit_date = exit_date.date()  # Define final_exit_date for non-selected companies
            
            with cols[5]:
                if is_selected and first_sale_date and final_exit_date:
                    # Calculate number of distributions based on date range
                    months_diff = (final_exit_date.year - first_sale_date.year) * 12 + (final_exit_date.month - first_sale_date.month)
                    if frequency == 'quarterly':
                        calc_distributions = max(1, (months_diff // 3) + 1)
                    elif frequency == 'annual':
                        calc_distributions = max(1, (months_diff // 12) + 1)
                    elif frequency == 'custom':
                        calc_distributions = max(1, (months_diff // months_between_distributions) + 1)
                    else:  # semi-annual
                        calc_distributions = max(1, (months_diff // 6) + 1)
                    
                    st.write(f"{calc_distributions}")
                else:
                    st.write("-")
            
            # Calculate distribution schedule if selected
            if is_selected and first_sale_date and final_exit_date:
                # Calculate company-specific fees directly
                company_fee_entry = None
                
                # Convert dates to pandas Timestamps for proper comparison
                exit_date_ts = pd.to_datetime(exit_date)
                final_exit_date_ts = pd.to_datetime(final_exit_date)
                
                # Determine if this is a first exit (companies exiting on earliest date)
                earliest_exit_date = edited_df['Exit Date'].min()
                is_first_exit = (exit_date_ts <= earliest_exit_date)
                
                # Get fee parameters from session state or defaults
                fee_balance = st.session_state.get('fund_fee_balance', 4000000)  # Default from typical fund
                fee_date = st.session_state.get('fund_fee_date', pd.to_datetime('2024-09-30'))
                q_fee = st.session_state.get('fund_quarterly_fee', 19194832.0)  # Sum of all company fees
                
                # Always calculate company-specific fees directly for IPO scenario
                company_fee_entry = calculate_company_specific_ipo_fees(
                    company_name, final_exit_date_ts, fee_balance, fee_date, q_fee, is_first_exit
                )
                
                # Get original investment amount for accurate fee calculations
                original_investment = company_net_investments.get(company_name, 0)
                
                schedule = calculate_ipo_distributions_advanced(
                    company_name, 
                    exit_value, 
                    final_exit_date_ts,  # Now represents final exit date
                    first_sale_date=pd.to_datetime(first_sale_date),  # NEW: Pass first sale date as Timestamp
                    distribution_years=distribution_years,
                    distribution_pattern=pattern,
                    custom_percentages=custom_percentages,
                    distribution_frequency=frequency,
                    custom_months_between=months_between_distributions if frequency == 'custom' else None,
                    original_investment=original_investment
                )
                
                # Apply enhanced fees to distributions using decremental method
                if company_fee_entry:
                    schedule = apply_enhanced_fees_to_ipo_distributions(
                        schedule, 
                        company_fee_entry, 
                        is_first_exit
                    )
                
                ipo_schedules.append(schedule)
        
        # Update session state
        st.session_state.ipo_selected_companies = set(selected_companies)
        st.session_state.ipo_scenario_changed = True
        st.session_state.ipo_schedules = ipo_schedules  # Store IPO schedules for details section
        
        # Display distribution preview if companies are selected
        if ipo_schedules:
            st.markdown("#### 📊 Distribution Schedule Preview")
            
            # Create summary metrics
            total_exit_value = sum(s['exit_value'] for s in ipo_schedules)
            num_companies = len(ipo_schedules)
            
            metric_cols = st.columns(4)
            with metric_cols[0]:
                st.metric("IPO Companies", num_companies)
            with metric_cols[1]:
                st.metric("Total Exit Value", f"${total_exit_value:,.0f}")
            with metric_cols[2]:
                st.metric("Distribution Period", f"{distribution_years} Years")
            with metric_cols[3]:
                st.metric("Frequency", frequency.replace('-', ' ').title())
            

            
            # Show detailed distribution information
            with st.expander("View Detailed Distribution Information"):
                # Create detailed view with enhanced fee breakdown
                detailed_data = []
                for schedule in ipo_schedules:
                    company_name = schedule['company_name']
                    has_fees = 'distribution_amounts_after_fees' in schedule
                    is_first_exit = schedule.get('is_first_exit', False)
                    
                    for i, (date, gross_amt, pct) in enumerate(zip(
                        schedule['distribution_dates'],
                        schedule['distribution_amounts'],
                        schedule['distribution_percentages']
                    )):
                        if has_fees:
                            net_amt = schedule['distribution_amounts_after_fees'][i]
                            mgmt_fee = schedule.get('management_fee_amounts', [0] * len(schedule['distribution_dates']))[i]
                            cc_fee = schedule.get('capital_call_fee_amounts', [0] * len(schedule['distribution_dates']))[i]
                            pr_fee = schedule.get('priority_return_amounts', [0] * len(schedule['distribution_dates']))[i]
                            total_fee = mgmt_fee + cc_fee + pr_fee
                        else:
                            net_amt = gross_amt
                            mgmt_fee = cc_fee = pr_fee = total_fee = 0
                        
                        # Build fee detail string
                        fee_details = []
                        if mgmt_fee > 0:
                            fee_details.append(f"Mgmt: ${mgmt_fee:,.0f}")
                        if cc_fee > 0:
                            fee_details.append(f"CC: ${cc_fee:,.0f}")
                        if pr_fee > 0:
                            fee_details.append(f"PR: ${pr_fee:,.0f}")
                        
                        fee_detail_str = " | ".join(fee_details) if fee_details else "No fees"
                        
                        # Add enhanced emoji indicators
                        if i == 0:
                            exit_indicator = "🏁 Exit"
                        else:
                            exit_indicator = f"📅 Distribution {i}"
                        
                        detailed_data.append({
                            'Company': company_name,
                            'Event': exit_indicator,
                            'Date': date.strftime('%Y-%m-%d'),
                            'Gross Amount': f"${gross_amt:,.0f}",
                            'Fee Details': fee_detail_str,
                            'Net Amount': f"${net_amt:,.0f}",
                            'Percentage': f"{pct:.1f}%"
                        })
                
                if detailed_data:
                    detailed_df = pd.DataFrame(detailed_data)
                    st.dataframe(detailed_df, use_container_width=True)
                    
                    # Show fee summary if fees are present
                    total_fees = sum(schedule.get('total_fees', 0) for schedule in ipo_schedules)
                    if total_fees > 0:
                        st.markdown(f"**Total Estimated Fees**: ${total_fees:,.0f}")
                        st.caption("🔹 **Decremental Allocation**: Management fees accrue quarterly but decrease as shares are distributed over time")

            return ipo_schedules
        else:
            st.info("👆 Select companies above to see distribution preview")
            # Clear IPO schedules from session state when no companies selected
            st.session_state.ipo_schedules = []
            return None

    # Clear IPO schedules from session state when IPO scenario is disabled
    st.session_state.ipo_schedules = []
    return None

# Fee allocation function removed - now using decremental method by default 