/* Main container and viewport fixes */
html, body {
    overflow-x: hidden !important;
    max-width: 100vw !important;
    margin: 0;
    padding: 0;
}

.stApp {
    overflow-x: hidden !important;
    max-width: 100vw !important;
}

/* Main content container */
.main .block-container {
    max-width: 100% !important;
    overflow-x: hidden !important;
    padding-left: 1rem !important;
    padding-right: 1rem !important;
    margin: 0 auto !important;
}

/* Sidebar width control */
section[data-testid="stSidebar"] {
    width: 21rem !important;
    min-width: 21rem !important;
    max-width: 21rem !important;
    overflow-x: hidden !important;
}

/* Global element constraints */
.element-container {
    max-width: 100% !important;
    overflow-x: hidden !important;
}

/* Horizontal block containers */
div[data-testid="stHorizontalBlock"] {
    overflow-x: hidden !important;
    max-width: 100% !important;
}

div[data-testid="column"] {
    max-width: 100% !important;
    overflow-x: hidden !important;
}

/* Charts and plots - More aggressive targeting */
[data-testid="stPlotlyChart"],
[data-testid="stPlotlyChart"] > div,
[data-testid="stPlotlyChart"] > div > div,
.js-plotly-plot,
.plotly,
.plotly-graph-div {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
    box-sizing: border-box !important;
}

/* Force plotly to respect container */
.plotly .main-svg {
    max-width: 100% !important;
}

/* Tables and dataframes - More specific targeting */
[data-testid="stDataFrame"],
[data-testid="stDataFrame"] > div,
[data-testid="stDataFrame"] > div > div,
[data-testid="stTable"],
[data-testid="stTable"] > div,
[data-testid="stTable"] > div > div,
.stDataFrame,
.stTable {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: auto !important;
    box-sizing: border-box !important;
}

/* Table structure */
table, 
.dataframe,
[data-testid="stDataFrame"] table,
[data-testid="stTable"] table {
    width: 100% !important;
    max-width: 100% !important;
    table-layout: auto !important;
    word-wrap: break-word !important;
    border-collapse: collapse !important;
    box-sizing: border-box !important;
}

/* Table cells */
td, th,
[data-testid="stDataFrame"] td,
[data-testid="stDataFrame"] th,
[data-testid="stTable"] td,
[data-testid="stTable"] th {
    padding: 4px 8px !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    max-width: 200px !important;
    box-sizing: border-box !important;
}

/* Specific column widths for better control */
[data-testid="stDataFrame"] th:nth-child(1),
[data-testid="stDataFrame"] td:nth-child(1) {
    max-width: 150px !important;
    min-width: 120px !important;
}

[data-testid="stDataFrame"] th:nth-child(2),
[data-testid="stDataFrame"] td:nth-child(2),
[data-testid="stDataFrame"] th:nth-child(3),
[data-testid="stDataFrame"] td:nth-child(3) {
    max-width: 120px !important;
    min-width: 100px !important;
}

[data-testid="stDataFrame"] th:nth-child(4),
[data-testid="stDataFrame"] td:nth-child(4) {
    max-width: 80px !important;
    min-width: 60px !important;
}

/* Metrics containers */
div[data-testid="metric-container"] {
    max-width: 100% !important;
    overflow-x: hidden !important;
}

/* Expander content */
.st-expander .st-dataframe,
.st-expander .st-dataframe table {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: auto !important;
}

.st-expander .st-dataframe th,
.st-expander .st-dataframe td {
    max-width: 150px !important;
    white-space: normal !important;
    word-wrap: break-word !important;
}

/* Button and input controls */
.stButton button,
.stSelectbox,
.stNumberInput,
.stTextInput {
    max-width: 100% !important;
}

/* ===== DROPDOWN FIX - COMPREHENSIVE SOLUTION ===== */

/* Reset any interfering styles on selectbox */
[data-testid="stSelectbox"] {
    position: relative !important;
    overflow: visible !important;
    max-width: 100% !important;
}

[data-testid="stSelectbox"] > div {
    overflow: visible !important;
}

[data-testid="stSelectbox"] [data-baseweb="select"] {
    overflow: visible !important;
}

/* Dropdown menu container */
[data-baseweb="popover"] {
    z-index: 999999 !important;
    position: fixed !important;
    overflow: visible !important;
    max-width: none !important;
    width: auto !important;
}

/* Dropdown menu list */
[data-baseweb="menu"] {
    z-index: 999999 !important;
    position: relative !important;
    overflow: visible !important;
    max-width: 300px !important;
    min-width: 150px !important;
    background: white !important;
    border: 1px solid #ccc !important;
    border-radius: 4px !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
}

/* Dropdown menu options */
[data-baseweb="menu"] ul {
    max-height: 200px !important;
    overflow-y: auto !important;
    padding: 0 !important;
    margin: 0 !important;
}

[data-baseweb="menu"] li {
    padding: 8px 12px !important;
    cursor: pointer !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    pointer-events: auto !important;
}

[data-baseweb="menu"] li:hover {
    background-color: #f0f0f0 !important;
}

/* Sidebar specific fixes */
section[data-testid="stSidebar"] [data-testid="stSelectbox"] {
    position: relative !important;
    overflow: visible !important;
}

section[data-testid="stSidebar"] [data-baseweb="popover"] {
    position: fixed !important;
    z-index: 999999 !important;
    left: auto !important;
    right: auto !important;
}

/* Ensure sidebar doesn't clip dropdowns */
section[data-testid="stSidebar"] {
    overflow: visible !important;
}

section[data-testid="stSidebar"] > div {
    overflow: visible !important;
}

section[data-testid="stSidebar"] [data-testid="stExpander"] {
    overflow: visible !important;
}

section[data-testid="stSidebar"] [data-testid="stExpander"] > div {
    overflow: visible !important;
}

/* ===== END DROPDOWN FIX ===== */

/* Nuclear option - Apply to ALL elements except selectbox dropdowns */
* {
    box-sizing: border-box !important;
}

/* Ensure no interference with dropdown elements - DISABLED FOR TESTING */
/* *:not(html):not(body):not([data-baseweb="popover"]):not([data-baseweb="menu"]):not([data-baseweb="select"]):not([data-baseweb="popover"] *):not([data-baseweb="menu"] *) {
    max-width: 100% !important;
} */

/* Ensure no element can extend beyond viewport */
.main > div {
    max-width: 100% !important;
    overflow-x: hidden !important;
}

/* Force all Streamlit components to respect viewport - except selectbox dropdowns - DISABLED FOR TESTING */
/* [data-testid]:not([data-testid*="stSelectbox"]):not([data-testid="stSelectbox"]) {
    max-width: 100% !important;
    overflow-x: hidden !important;
} */

/* Specific override for dynamically loaded content - DISABLED FOR TESTING */
/* .streamlit-container:not([data-baseweb="popover"]):not([data-baseweb="menu"]),
.streamlit-expanderContent:not([data-baseweb="popover"]):not([data-baseweb="menu"]),
[class*="streamlit"]:not([data-baseweb="popover"]):not([data-baseweb="menu"]),
[id*="streamlit"]:not([data-baseweb="popover"]):not([data-baseweb="menu"]) {
    max-width: 100% !important;
    overflow-x: hidden !important;
} */

/* Final plotly fix */
.plotly-graph-div > div {
    max-width: 100% !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .main .block-container {
        padding-left: 0.5rem !important;
        padding-right: 0.5rem !important;
    }
    
    [data-testid="stDataFrame"] th,
    [data-testid="stDataFrame"] td {
        max-width: 100px !important;
        font-size: 0.8rem !important;
    }
}

/* Apply width constraints to prevent overflow */
.width-enforcer {
    max-width: 100% !important;
    overflow-x: hidden !important;
}

/* Apply the width enforcer to common containers */
.stApp,
.main,
.block-container,
.element-container,
[data-testid="stHorizontalBlock"],
[data-testid="column"],
[data-testid="stDataFrame"],
[data-testid="stTable"],
[data-testid="stPlotlyChart"],
[data-testid="metric-container"] {
    max-width: 100% !important;
    overflow-x: hidden !important;
}

/* Data Editor height fixes */
div[data-testid="stDataEditor"] {
    max-width: 100% !important;
    overflow-x: hidden !important;
    min-height: auto !important;
    height: auto !important;
}

div[data-testid="stDataEditor"] > div {
    max-width: 100% !important;
    overflow-x: hidden !important;
    height: auto !important;
}

/* Remove height constraints from containers that might wrap data editor */
.stApp .main .block-container {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
}

/* Ensure element containers don't constrain height */
.element-container {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
} 