# capital_call_fees.py
# Updated version with ACTUAL capital call data from fund records - OPTIMIZED

import pandas as pd
import numpy as np
from datetime import datetime
from math import pow
from typing import Dict, List, Tuple, Optional
from functools import lru_cache
import hashlib

# Enhanced cache for capital call calculations with better key handling
_capital_call_cache = {}
_capital_call_data_cache = None

# Pre-computed constants for faster calculations
DAYS_PER_YEAR = 365.0
QUARTERS_PER_YEAR = 4.0

@lru_cache(maxsize=128)
def _calculate_priority_return_vectorized(amounts_tuple: tuple, days_tuple: tuple, hurdle_rate: float = 0.08) -> tuple:
    """
    Vectorized priority return calculation with caching.
    Uses tuples for hashable cache keys.
    """
    amounts = np.array(amounts_tuple)
    days = np.array(days_tuple)
    
    years_to_exit = days / DAYS_PER_YEAR
    calculated_pr = np.where(
        days > 0,
        amounts * (np.power(1 + hurdle_rate, years_to_exit) - 1),
        0
    )
    
    return tuple(calculated_pr)

def load_capital_call_fees(excel_file_path: str = "2025.03.31 Fund VII Scenario Analysis v2.xlsx") -> Dict:
    """
    Load capital call fees using ACTUAL call data from fund records.
    OPTIMIZED: Uses global cache to avoid repeated Excel reads.
    """
    global _capital_call_data_cache
    
    # Return cached data if available
    if _capital_call_data_cache is not None:
        return _capital_call_data_cache
    
    try:
        # Check if Excel file exists and try to read it
        import os
        if os.path.exists(excel_file_path):
            try:
                df = pd.read_excel(excel_file_path, sheet_name="Fees and Expense PR", header=None)
                
                # Find the TOTAL FUND row
                total_fund_row_idx = None
                for idx, row in df.iterrows():
                    if pd.notna(row.iloc[0]) and 'TOTAL FUND' in str(row.iloc[0]).upper():
                        total_fund_row_idx = idx
                        break
                
                if total_fund_row_idx is not None:
                    total_row = df.iloc[total_fund_row_idx]
                    
                    # Extract Excel reference data (columns 74 and 75)
                    try:
                        total_capital_str = str(total_row.iloc[73]).replace(',', '').replace('$', '')
                        excel_total_capital = float(total_capital_str) if total_capital_str != 'nan' else 288277746
                        
                        excel_pr_str = str(total_row.iloc[74]).replace(',', '').replace('$', '')
                        excel_priority_return = float(excel_pr_str) if excel_pr_str != 'nan' else 99528942
                        
                    except (IndexError, ValueError) as e:
                        excel_total_capital = 288277746
                        excel_priority_return = 99528942
                        
            except Exception as e:
                excel_total_capital = 288277746
                excel_priority_return = 99528942
        else:
            excel_total_capital = 288277746
            excel_priority_return = 99528942
        
        # Use ACTUAL capital call data from fund records
        capital_calls = create_actual_capital_calls_with_dates()
        
        # Verify total matches actual records
        actual_total = sum(call['amount'] for call in capital_calls)
        
        result = {
            'capital_calls': capital_calls,
            'total_capital_called': actual_total,  # ACTUAL from fund records: $288,277,746
            'excel_priority_return': excel_priority_return,  # For reference only
            'by_investor': {}  # Not extracting individual investor data for now
        }
        
        # Cache the result globally
        _capital_call_data_cache = result
        return result
        
    except Exception as e:
        result = get_actual_capital_call_data()
        _capital_call_data_cache = result
        return result


def create_actual_capital_calls_with_dates() -> List[Dict]:
    """
    Create capital calls using ACTUAL call data from fund records.
    These are the real calls with actual dates and amounts.
    """
    # ACTUAL capital call data from fund records
    actual_calls_data = [
        (1, '2020-04-15', 'Capital Call - Placement Fees', 2169373),
        (2, '2020-04-15', 'Capital Call - Management Fees', 31524861),
        (3, '2020-04-15', 'Capital Call - Organization Expense', 5270712),
        (4, '2020-04-15', 'Capital Call - Partnership Expenses', 9846154),
        (5, '2020-07-01', 'Capital Call - Management Fees', 27191846),
        (6, '2020-07-01', 'Capital Call - Partnership Expenses', 2953846),
        (7, '2020-09-10', 'Capital Call - Placement Fees', 2151484),
        (8, '2020-09-10', 'Capital Call - Management Fees', 74133),
        (9, '2021-01-05', 'Capital Call - Management Fees', 24809749),
        (10, '2021-04-16', 'Capital Call - Placement Fee', 2125553),
        (11, '2021-04-16', 'Capital Call - Management Fees', 608579),
        (12, '2021-04-16', 'Capital Call - Partnership Expenses', 4923077),
        (13, '2021-07-01', 'Capital Call - Partnership Expenses & Mgmt Fee', 6358529),
        (14, '2021-10-01', 'Capital Call - Partnership Expenses, Mgmt Fee & Placement Fee', 22405031),
        (15, '2022-01-05', 'Capital Call - Partnership Expenses & Mgmt Fee', 31249553),
        (16, '2022-04-01', 'Capital Call - Partnership Expenses, Mgmt Fee & Placement Fee', 33636132),
        (17, '2022-05-23', 'Capital Call - Return of Excess Mgmt Fees, Partnership Expenses', -2439738),
        (18, '2022-09-27', 'Capital Call - Partnership Expenses, & Placement Fee', 6973054),
        (19, '2023-07-06', 'Capital Call - Management Fees', 20507501),
        (20, '2023-09-29', 'Capital Call - Management Fees', 18908580),
        (21, '2024-01-09', 'Capital Call - Management Fees', 18990766),
        (22, '2024-07-16', 'Capital Call - Management Fees', 15085125),
        (23, '2024-07-16', 'Capital Call - Partnership Expenses', 2953846)
    ]
    
    capital_calls = []
    
    for call_number, call_date_str, description, amount in actual_calls_data:
        capital_calls.append({
            'call_number': call_number,
            'call_type': description,
            'call_date': pd.Timestamp(call_date_str),
            'amount': amount,
            'priority_return': 0,  # Will be calculated dynamically
            'returned': False,
            'column_index': call_number
        })
    
    return capital_calls


def calculate_capital_call_fees_at_exit(capital_call_data: Dict, exit_date: pd.Timestamp, is_first_exit: bool = True, hurdle_rate: float = 0.08) -> Dict:
    """
    Calculate which capital call fees are due at a given exit date using ACTUAL call data.
    OPTIMIZED: Enhanced caching, vectorized calculations, and reduced object creation.
    """
    # Handle NaT (Not a Time) values - return empty result
    if pd.isna(exit_date) or exit_date is pd.NaT:
        return {
            'satisfied_calls': [],
            'unsatisfied_calls': [],
            'total_satisfied_amount': 0,
            'total_satisfied_pr': 0,
            'total_unsatisfied_amount': 0,
            'total_unsatisfied_pr': 0,
            'is_first_exit': is_first_exit,
            'calculated_pr_details': [],
            'excel_pr_reference': capital_call_data.get('excel_priority_return', 0)
        }
    
    # Create a more robust cache key
    exit_date_str = exit_date.strftime('%Y-%m-%d')
    cache_key = f"{exit_date_str}_{is_first_exit}_{hurdle_rate}"
    
    # Check cache first
    if cache_key in _capital_call_cache:
        return _capital_call_cache[cache_key]
    
    all_calls = capital_call_data.get('capital_calls', [])
    
    if not is_first_exit:
        # Quick return for non-first exits
        result = {
            'satisfied_calls': [],
            'unsatisfied_calls': [],
            'total_satisfied_amount': 0,
            'total_satisfied_pr': 0,
            'total_unsatisfied_amount': 0,
            'total_unsatisfied_pr': 0,
            'is_first_exit': is_first_exit,
            'calculated_pr_details': [],
            'excel_pr_reference': capital_call_data.get('excel_priority_return', 0)
        }
        _capital_call_cache[cache_key] = result
        return result
    
    # Pre-allocate arrays for vectorized calculations
    n_calls = len(all_calls)
    call_amounts = np.zeros(n_calls)
    days_to_exit = np.zeros(n_calls)
    
    # Fill arrays efficiently
    for i, call in enumerate(all_calls):
        call_amounts[i] = call['amount']
        days_to_exit[i] = (exit_date - call['call_date']).days
    
    # Use cached vectorized calculation
    amounts_tuple = tuple(call_amounts)
    days_tuple = tuple(days_to_exit)
    calculated_pr = np.array(_calculate_priority_return_vectorized(amounts_tuple, days_tuple, hurdle_rate))
    
    total_calculated_pr = np.sum(calculated_pr)
    years_to_exit = days_to_exit / DAYS_PER_YEAR
    
    # Create enhanced calls list efficiently - only create if needed for display
    satisfied_calls = []
    calculated_pr_details = []
    
    # Only create detailed objects if they might be used
    for i, call in enumerate(all_calls):
        enhanced_call = {
            'call_number': call['call_number'],
            'call_type': call['call_type'],
            'call_date': call['call_date'],
            'amount': call['amount'],
            'calculated_pr': calculated_pr[i],
            'days_to_exit': days_to_exit[i],
            'years_to_exit': years_to_exit[i],
            'original_pr': call['priority_return']
        }
        satisfied_calls.append(enhanced_call)
        
        # Only create detailed calculation strings if needed
        calculated_pr_details.append({
            'call_number': call['call_number'],
            'call_type': call['call_type'],
            'call_date': call['call_date'],
            'amount': call['amount'],
            'days_to_exit': days_to_exit[i],
            'years_to_exit': years_to_exit[i],
            'calculated_pr': calculated_pr[i],
            'pr_calculation': f"${call['amount']:,.0f} × (1.08^{years_to_exit[i]:.3f} - 1) = ${calculated_pr[i]:,.0f}"
        })
    
    # Use the ACTUAL principal amount from fund records
    actual_total_principal = capital_call_data.get('total_capital_called', 288277746)
    
    result = {
        'satisfied_calls': satisfied_calls,
        'unsatisfied_calls': [],
        'total_satisfied_amount': actual_total_principal,
        'total_satisfied_pr': total_calculated_pr,
        'total_unsatisfied_amount': 0,
        'total_unsatisfied_pr': 0,
        'is_first_exit': is_first_exit,
        'calculated_pr_details': calculated_pr_details,
        'excel_pr_reference': capital_call_data.get('excel_priority_return', 0)
    }
    
    # Cache the result
    _capital_call_cache[cache_key] = result
    return result


def calculate_management_fees_with_capital_calls(
    balance_date: pd.Timestamp,
    exit_date: pd.Timestamp,
    initial_balance: float,
    quarterly_fee: float,
    capital_call_data: Dict,
    hurdle_rate: float = 0.08,
    is_first_exit: bool = True
) -> Dict:
    """
    Calculate management fees and capital call fees using ACTUAL call data.
    Priority returns are ONLY calculated on capital call fees, not management fees.
    Updated to use actual fund records.
    """
    # Handle NaT (Not a Time) values - return zero fees
    if pd.isna(exit_date) or exit_date is pd.NaT:
        return {
            'mgmt_total_fees': 0,
            'mgmt_initial_balance': initial_balance,
            'mgmt_new_fees': 0,
            'mgmt_priority_return': 0,
            'mgmt_fee_basis': initial_balance,
            'capital_call_satisfied_amount': 0,
            'capital_call_satisfied_pr': 0,
            'capital_call_unsatisfied_amount': 0,
            'capital_call_unsatisfied_pr': 0,
            'satisfied_calls': [],
            'unsatisfied_calls': [],
            'is_first_exit': is_first_exit,
            'calculated_pr_details': [],
            'total_fees': 0,
            'total_basis': initial_balance,
            'total_priority_return': 0,
            'days': 0
        }
    
    # Calculate management fees WITHOUT priority return
    # Ensure both dates are pandas Timestamps for comparison
    exit_date = pd.to_datetime(exit_date)
    balance_date = pd.to_datetime(balance_date)
    
    if exit_date <= balance_date:
        mgmt_fee_results = {
            'total_fees': 0,
            'initial_balance': initial_balance,
            'new_fees': 0,
            'priority_return': 0,  # No PR on management fees
            'days': 0,
            'fee_basis': initial_balance
        }
    else:
        # Calculate days between dates
        days_diff = (exit_date - balance_date).days
        
        # Calculate number of quarters
        quarters = days_diff / 91.25
        
        # Calculate new fees accrued since balance date
        new_fees = quarterly_fee * quarters
        
        # Total management fee basis (initial balance plus new fees)
        total_mgmt_fee_basis = initial_balance + new_fees
        
        # NO priority return on management fees
        mgmt_fee_results = {
            'total_fees': total_mgmt_fee_basis,  # Just the fees, no PR
            'initial_balance': initial_balance,
            'new_fees': new_fees,
            'priority_return': 0,  # No PR on management fees
            'days': days_diff,
            'fee_basis': total_mgmt_fee_basis
        }
    
    # Calculate capital call fees with priority returns (only at first exit)
    capital_call_results = calculate_capital_call_fees_at_exit(
        capital_call_data, exit_date, is_first_exit, hurdle_rate
    )
    
    # Combine results using actual fund records
    total_fees = (mgmt_fee_results['total_fees'] +  # Management fees (no PR)
                  capital_call_results['total_satisfied_amount'] +  # Capital call principal
                  capital_call_results['total_satisfied_pr'])  # Capital call PR only
    
    return {
        # Management fee components (NO priority return)
        'mgmt_total_fees': mgmt_fee_results['total_fees'],
        'mgmt_initial_balance': mgmt_fee_results['initial_balance'],
        'mgmt_new_fees': mgmt_fee_results['new_fees'],
        'mgmt_priority_return': 0,  # No PR on management fees
        'mgmt_fee_basis': mgmt_fee_results['fee_basis'],
        
        # Capital call fee components (WITH priority return) - ACTUAL FUND DATA
        'capital_call_satisfied_amount': capital_call_results['total_satisfied_amount'],  # $288,277,746
        'capital_call_satisfied_pr': capital_call_results['total_satisfied_pr'],  # Dynamic calculation
        'capital_call_unsatisfied_amount': capital_call_results['total_unsatisfied_amount'],
        'capital_call_unsatisfied_pr': capital_call_results['total_unsatisfied_pr'],
        'satisfied_calls': capital_call_results['satisfied_calls'],
        'unsatisfied_calls': capital_call_results['unsatisfied_calls'],
        'is_first_exit': is_first_exit,
        'calculated_pr_details': capital_call_results.get('calculated_pr_details', []),
        
        # Combined totals
        'total_fees': total_fees,
        'total_basis': (mgmt_fee_results['fee_basis'] + 
                       capital_call_results['total_satisfied_amount']),
        'total_priority_return': capital_call_results['total_satisfied_pr'],  # Only from capital calls
        
        # Other info
        'days': mgmt_fee_results['days']
    }


def calculate_fund_fees_schedule_with_capital_calls(
    exits_df: pd.DataFrame,
    initial_fee_balance: float,
    fee_balance_date: pd.Timestamp,
    quarterly_fee: float,
    capital_call_data: Dict,
    hurdle_rate: float = 0.08
) -> List[Dict]:
    """
    Calculate the fee schedule for multiple exits including capital call fees.
    Capital call fees are all satisfied and paid at the FIRST exit only.
    Updated to use actual fund records.
    """
    # Sort exits by date
    exits_sorted = exits_df.sort_values('Exit Date').reset_index(drop=True)
    
    fee_schedule = []
    current_balance = initial_fee_balance
    current_date = fee_balance_date
    
    for idx, exit in exits_sorted.iterrows():
        exit_date = pd.to_datetime(exit['Exit Date'])
        is_first_exit = (idx == 0)  # First exit in the sorted list
        
        # Calculate fees including capital calls (only at first exit)
        fee_details = calculate_management_fees_with_capital_calls(
            current_date, 
            exit_date, 
            current_balance, 
            quarterly_fee,
            capital_call_data,
            hurdle_rate,
            is_first_exit
        )
        
        # Record the fee payment
        fee_schedule.append({
            'Exit Number': idx + 1,
            'Company': exit['Company'],
            'Exit Date': exit_date,
            'Exit Value': exit.get('Exit Value', 0),
            'Days Since Last Payment': fee_details['days'],
            'Is First Exit': is_first_exit,
            
            # Management fees
            'Mgmt Fee Starting Balance': current_balance,
            'Mgmt Fee New Fees': fee_details['mgmt_new_fees'],
            'Mgmt Fee Basis': fee_details['mgmt_fee_basis'],
            'Mgmt Fee Priority Return': fee_details['mgmt_priority_return'],
            'Mgmt Fee Total': fee_details['mgmt_total_fees'],
            
            # Capital call fees (only at first exit) - ACTUAL FUND DATA
            'Capital Calls Satisfied': len(fee_details['satisfied_calls']),
            'Capital Call Amount': fee_details['capital_call_satisfied_amount'],  # $288,277,746
            'Capital Call PR': fee_details['capital_call_satisfied_pr'],  # Dynamic calculation
            
            # Totals
            'Total Fees Due': fee_details['total_fees'],
            'Net Exit Value': exit.get('Exit Value', 0) - fee_details['total_fees']
        })
        
        # Reset for next period - management fee balance goes to zero
        current_balance = 0
        current_date = exit_date
    
    return fee_schedule


def get_actual_capital_call_data() -> Dict:
    """
    Return actual capital call data structure using ACTUAL fund records.
    Priority return will be calculated dynamically based on exit dates.
    """
    # Create actual capital calls with real dates and amounts
    capital_calls = create_actual_capital_calls_with_dates()
    
    # Calculate total from actual calls
    total_capital_called = sum(call['amount'] for call in capital_calls)
    excel_priority_return = 99528942  # From Excel column 75 (reference only)
    
    return {
        'capital_calls': capital_calls,
        'total_capital_called': total_capital_called,  # ACTUAL: $288,277,746
        'excel_priority_return': excel_priority_return,  # Reference only
        'by_investor': {}
    }


def get_empty_capital_call_data() -> Dict:
    """Return empty capital call data structure"""
    return {
        'capital_calls': [],
        'total_capital_called': 0,
        'total_priority_return': 0,
        'by_investor': {},
        'distribution_date': pd.Timestamp.now()
    }


def clear_capital_call_cache():
    """Clear the capital call calculation cache."""
    global _capital_call_cache, _capital_call_data_cache
    _capital_call_cache.clear()
    _capital_call_data_cache = None

def get_capital_call_summary_fast(capital_call_data: Dict, exit_date: pd.Timestamp, is_first_exit: bool = True, hurdle_rate: float = 0.08) -> Dict:
    """
    Fast version that only returns summary totals without detailed objects.
    Optimized for sensitivity analysis and bulk calculations.
    """
    # Handle NaT (Not a Time) values - return empty result
    if pd.isna(exit_date) or exit_date is pd.NaT:
        return {
            'total_satisfied_amount': 0,
            'total_satisfied_pr': 0,
            'is_first_exit': is_first_exit
        }
    
    if not is_first_exit:
        return {
            'total_satisfied_amount': 0,
            'total_satisfied_pr': 0,
            'is_first_exit': is_first_exit
        }
    
    # Create cache key for summary
    exit_date_str = exit_date.strftime('%Y-%m-%d')
    cache_key = f"summary_{exit_date_str}_{hurdle_rate}"
    
    if cache_key in _capital_call_cache:
        return _capital_call_cache[cache_key]
    
    all_calls = capital_call_data.get('capital_calls', [])
    
    # Vectorized calculation without creating detailed objects
    call_amounts = np.array([call['amount'] for call in all_calls])
    days_to_exit = np.array([(exit_date - call['call_date']).days for call in all_calls])
    
    # Use cached vectorized calculation
    amounts_tuple = tuple(call_amounts)
    days_tuple = tuple(days_to_exit)
    calculated_pr = np.array(_calculate_priority_return_vectorized(amounts_tuple, days_tuple, hurdle_rate))
    
    result = {
        'total_satisfied_amount': capital_call_data.get('total_capital_called', 288277746),
        'total_satisfied_pr': np.sum(calculated_pr),
        'is_first_exit': is_first_exit
    }
    
    _capital_call_cache[cache_key] = result
    return result


# Example usage
if __name__ == "__main__":
    # Load actual capital call fees
    capital_call_data = load_capital_call_fees()
    
    # Example: Calculate fees at different exit dates to show dynamic calculation
    test_exit_dates = [
        pd.Timestamp('2025-09-30'),  # Earlier exit
        pd.Timestamp('2026-12-31'),  # Later exit
        pd.Timestamp('2027-06-30')   # Much later exit
    ]
    
    for exit_date in test_exit_dates:
        fees_at_exit = calculate_capital_call_fees_at_exit(capital_call_data, exit_date)
        total_fees = fees_at_exit['total_satisfied_amount'] + fees_at_exit['total_satisfied_pr']
        
        print(f"\nExit Date: {exit_date.date()}")
        print(f"  Actual Principal: ${fees_at_exit['total_satisfied_amount']:,.2f}")
        print(f"  Dynamic PR (8%): ${fees_at_exit['total_satisfied_pr']:,.2f}")
        print(f"  TOTAL FEES: ${total_fees:,.2f}")
        
        # Show difference from Excel reference
        excel_ref = capital_call_data.get('excel_priority_return', 0)
        if excel_ref > 0:
            pr_diff = fees_at_exit['total_satisfied_pr'] - excel_ref
            print(f"  PR vs Excel: {'+' if pr_diff >= 0 else ''}${pr_diff:,.2f}")
    
    print(f"\nNote: Priority return varies based on exit timing, but principal remains constant at ${capital_call_data['total_capital_called']:,.0f}")
